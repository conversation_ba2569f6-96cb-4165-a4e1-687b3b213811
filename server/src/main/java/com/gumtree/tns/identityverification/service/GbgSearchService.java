package com.gumtree.tns.identityverification.service;

import com.gumtree.tns.identityverification.gateway.client.dto.GbgApiClientDtos.*;
import com.gumtree.tns.identityverification.service.dto.SearchRequestDto;
import com.gumtree.tns.identityverification.service.dto.SearchResponseDto;

/**
 * Service for handling GBG company search operations
 */
public interface GbgSearchService {

    /**
     * Search for companies using GBG API
     * 
     * @param searchRequest the search criteria
     * @param userId the user ID performing the search (optional)
     * @param accountId the account ID performing the search (optional)
     * @return search results with company information
     */
    SearchResponseDto searchCompanies(SearchRequestDto searchRequest, Long userId, Long accountId);

    /**
     * Get search history for a user or account
     * 
     * @param userId the user ID (optional)
     * @param accountId the account ID (optional)
     * @param limit maximum number of results to return
     * @return list of previous searches
     */
    SearchResponseDto getSearchHistory(Long userId, Long accountId, Integer limit);

    /**
     * Get search results by lookup ID
     * 
     * @param lookupId the GBG lookup ID
     * @return search results
     */
    SearchResponseDto getSearchResults(Integer lookupId);
}
