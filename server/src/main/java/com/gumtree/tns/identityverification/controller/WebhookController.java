package com.gumtree.tns.identityverification.controller;

import com.gumtree.tns.identityverification.service.GbgWebhookService;
import com.gumtree.tns.identityverification.service.dto.WebhookEventDto;
import com.gumtree.tns.identityverification.service.dto.WebhookProcessingResultDto;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;

/**
 * Controller for handling GBG webhook events
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/webhooks")
@RequiredArgsConstructor
public class WebhookController {

    private final GbgWebhookService webhookService;
    private final MeterRegistry meterRegistry;
    private final Timer webhookProcessingTimer;

    public WebhookController(GbgWebhookService webhookService, MeterRegistry meterRegistry) {
        this.webhookService = webhookService;
        this.meterRegistry = meterRegistry;
        this.webhookProcessingTimer = Timer.builder("webhook.processing.duration")
            .description("Duration of webhook event processing")
            .register(meterRegistry);
    }

    /**
     * Handle incoming GBG webhook events
     * POST /api/v1/webhooks/gbg
     */
    @PostMapping("/gbg")
    public ResponseEntity<String> handleGbgWebhook(
            HttpServletRequest request,
            @RequestBody String payload) {

        String eventType = request.getHeader("X-GBG-Event-Type");
        String signature = request.getHeader("X-GBG-Signature");
        String eventId = request.getHeader("X-GBG-Event-ID");

        log.atInfo().log("Received GBG webhook event - Type: {}, ID: {}", eventType, eventId);

        return webhookProcessingTimer.recordCallable(() -> {
            try {
                // Increment webhook received counter
                Counter.builder("webhook.events.received.total")
                    .tag("event_type", eventType != null ? eventType : "unknown")
                    .tag("source", "gbg")
                    .register(meterRegistry)
                    .increment();

                // Validate required headers
                if (eventType == null || signature == null) {
                    log.atWarn().log("Webhook missing required headers - EventType: {}, Signature: {}", 
                        eventType, signature != null ? "[PRESENT]" : "[MISSING]");
                    
                    Counter.builder("webhook.events.rejected.total")
                        .tag("reason", "missing_headers")
                        .tag("source", "gbg")
                        .register(meterRegistry)
                        .increment();
                    
                    return ResponseEntity.badRequest().body("Missing required headers");
                }

                // Verify signature first
                if (!webhookService.verifyWebhookSignature(payload, signature)) {
                    log.atWarn().log("Webhook signature verification failed for event: {}", eventId);
                    
                    Counter.builder("webhook.events.rejected.total")
                        .tag("reason", "invalid_signature")
                        .tag("source", "gbg")
                        .register(meterRegistry)
                        .increment();
                    
                    return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid signature");
                }

                // Process the webhook event
                WebhookProcessingResultDto result = webhookService.processWebhookEvent(payload, signature, eventType);

                if (result.getSuccess()) {
                    Counter.builder("webhook.events.processed.success.total")
                        .tag("event_type", eventType)
                        .tag("source", "gbg")
                        .register(meterRegistry)
                        .increment();

                    log.atInfo().log("Webhook event processed successfully - Type: {}, ID: {}, Status: {}", 
                        eventType, eventId, result.getStatus());
                    
                    return ResponseEntity.ok("Event processed successfully");
                } else {
                    Counter.builder("webhook.events.processed.error.total")
                        .tag("event_type", eventType)
                        .tag("error", result.getErrorCode() != null ? result.getErrorCode() : "unknown")
                        .tag("source", "gbg")
                        .register(meterRegistry)
                        .increment();

                    log.atError().log("Webhook event processing failed - Type: {}, ID: {}, Error: {}", 
                        eventType, eventId, result.getErrorDetails());

                    // Return 200 to prevent GBG from retrying if it's a permanent failure
                    if ("permanent_failure".equals(result.getErrorCode())) {
                        return ResponseEntity.ok("Event processing failed permanently");
                    }
                    
                    // Return 500 for temporary failures to trigger GBG retry
                    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body("Event processing failed: " + result.getMessage());
                }

            } catch (Exception e) {
                Counter.builder("webhook.events.processed.error.total")
                    .tag("event_type", eventType != null ? eventType : "unknown")
                    .tag("error", e.getClass().getSimpleName())
                    .tag("source", "gbg")
                    .register(meterRegistry)
                    .increment();

                log.atError().withCause(e).log("Unexpected error processing webhook event - Type: {}, ID: {}", 
                    eventType, eventId);
                
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Internal server error");
            }
        });
    }

    /**
     * Get webhook event history
     * GET /api/v1/webhooks/events
     */
    @GetMapping("/events")
    public ResponseEntity<List<WebhookEventDto>> getWebhookEvents(
            @RequestParam(value = "profileId", required = false) String profileId,
            @RequestParam(value = "eventType", required = false) String eventType,
            @RequestParam(value = "limit", defaultValue = "50") Integer limit) {

        log.atInfo().log("Webhook events request - profileId: {}, eventType: {}, limit: {}", 
            profileId, eventType, limit);

        try {
            List<WebhookEventDto> events = webhookService.getWebhookHistory(profileId, eventType, limit);

            log.atInfo().log("Retrieved {} webhook events", events.size());
            return ResponseEntity.ok(events);

        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to retrieve webhook events");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Retry failed webhook event
     * POST /api/v1/webhooks/events/{eventId}/retry
     */
    @PostMapping("/events/{eventId}/retry")
    public ResponseEntity<WebhookProcessingResultDto> retryWebhookEvent(
            @PathVariable("eventId") String eventId) {

        log.atInfo().log("Webhook event retry request for ID: {}", eventId);

        try {
            WebhookProcessingResultDto result = webhookService.retryWebhookProcessing(eventId);

            if (result.getSuccess()) {
                log.atInfo().log("Webhook event retry successful for ID: {}", eventId);
                return ResponseEntity.ok(result);
            } else {
                log.atWarn().log("Webhook event retry failed for ID: {}, Error: {}", eventId, result.getErrorDetails());
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result);
            }

        } catch (RuntimeException e) {
            if (e.getMessage().contains("not found")) {
                log.atWarn().log("Webhook event not found for retry: {}", eventId);
                return ResponseEntity.notFound().build();
            }
            
            log.atError().withCause(e).log("Failed to retry webhook event: {}", eventId);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get failed webhook events that need retry
     * GET /api/v1/webhooks/events/failed
     */
    @GetMapping("/events/failed")
    public ResponseEntity<List<WebhookEventDto>> getFailedWebhookEvents(
            @RequestParam(value = "maxRetries", defaultValue = "3") Integer maxRetries) {

        log.atInfo().log("Failed webhook events request with maxRetries: {}", maxRetries);

        try {
            List<WebhookEventDto> failedEvents = webhookService.getFailedWebhookEvents(maxRetries);

            log.atInfo().log("Retrieved {} failed webhook events", failedEvents.size());
            return ResponseEntity.ok(failedEvents);

        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to retrieve failed webhook events");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Webhook health check endpoint
     * GET /api/v1/webhooks/health
     */
    @GetMapping("/health")
    public ResponseEntity<String> healthCheck() {
        return ResponseEntity.ok("Webhook service is healthy");
    }

    /**
     * Test webhook endpoint for development/testing
     * POST /api/v1/webhooks/test
     */
    @PostMapping("/test")
    public ResponseEntity<String> testWebhook(@RequestBody String payload) {
        log.atInfo().log("Test webhook received with payload length: {}", payload.length());
        
        Counter.builder("webhook.events.test.total")
            .register(meterRegistry)
            .increment();
        
        return ResponseEntity.ok("Test webhook received successfully");
    }

    /**
     * Get webhook processing statistics
     * GET /api/v1/webhooks/statistics
     */
    @GetMapping("/statistics")
    public ResponseEntity<WebhookStatisticsDto> getWebhookStatistics() {
        log.atInfo().log("Webhook statistics request");

        try {
            // This would be implemented to gather statistics from the webhook service
            WebhookStatisticsDto stats = WebhookStatisticsDto.builder()
                .totalEventsReceived(0L) // Would be populated from metrics
                .totalEventsProcessed(0L)
                .totalEventsFailed(0L)
                .averageProcessingTimeMs(0.0)
                .build();

            return ResponseEntity.ok(stats);

        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to retrieve webhook statistics");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * DTO for webhook statistics
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class WebhookStatisticsDto {
        private Long totalEventsReceived;
        private Long totalEventsProcessed;
        private Long totalEventsFailed;
        private Double averageProcessingTimeMs;
        private java.time.LocalDateTime lastEventReceivedAt;
        private java.time.LocalDateTime lastEventProcessedAt;
        private java.util.Map<String, Long> eventTypeBreakdown;
        private java.util.Map<String, Long> processingStatusBreakdown;
    }
}
