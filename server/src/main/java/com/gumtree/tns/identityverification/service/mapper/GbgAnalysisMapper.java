package com.gumtree.tns.identityverification.service.mapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gumtree.tns.identityverification.dao.model.GbgAnalysisResult;
import com.gumtree.tns.identityverification.dao.model.GbgChecklist;
import com.gumtree.tns.identityverification.dao.model.GbgProfile;
import com.gumtree.tns.identityverification.gateway.client.dto.GbgApiClientDtos.*;
import com.gumtree.tns.identityverification.service.dto.AnalysisResponseDto;
import com.gumtree.tns.identityverification.service.dto.ChecklistResponseDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for GBG analysis operations
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GbgAnalysisMapper {

    private final ObjectMapper objectMapper;

    /**
     * Convert GbgAnalysisResponse to GbgAnalysisResult entity
     */
    public GbgAnalysisResult toAnalysisResult(GbgAnalysisResponse gbgAnalysis, GbgProfile profile) {
        return GbgAnalysisResult.builder()
            .gbgProfile(profile)
            .analysisId(generateAnalysisId(gbgAnalysis))
            .score(gbgAnalysis.getScore())
            .status(mapAnalysisStatus(gbgAnalysis.getStatus()))
            .riskLevel(mapRiskLevel(gbgAnalysis.getRisk()))
            .analysisReport(toJsonString(gbgAnalysis))
            .createdAt(parseDateTime(gbgAnalysis.getCreatedAt()))
            .updatedAt(LocalDateTime.now())
            .build();
    }

    /**
     * Convert GbgAnalysisResult entity to AnalysisResponseDto
     */
    public AnalysisResponseDto toAnalysisResponseDto(GbgAnalysisResult analysisResult) {
        GbgAnalysisResponse gbgAnalysis = fromJsonString(analysisResult.getAnalysisReport(), GbgAnalysisResponse.class);
        
        return AnalysisResponseDto.builder()
            .profileId(analysisResult.getGbgProfile().getGbgProfileUuid())
            .analysisId(analysisResult.getAnalysisId())
            .score(analysisResult.getScore())
            .status(analysisResult.getStatus().getValue())
            .riskLevel(analysisResult.getRiskLevel() != null ? analysisResult.getRiskLevel().getDisplayName() : null)
            .createdAt(analysisResult.getCreatedAt())
            .updatedAt(analysisResult.getUpdatedAt())
            .reports(mapAnalysisReports(gbgAnalysis))
            .riskSummary(buildRiskSummary(analysisResult))
            .build();
    }

    /**
     * Convert GbgChecklistResponse to GbgChecklist entity
     */
    public GbgChecklist toChecklistEntity(GbgChecklistResponse gbgChecklist, GbgProfile profile) {
        return GbgChecklist.builder()
            .gbgProfile(profile)
            .gbgChecklistId(gbgChecklist.getId())
            .name(gbgChecklist.getName())
            .uncompletedTasks(gbgChecklist.getUncompletedTasks())
            .completedTasks(gbgChecklist.getCompletedTasks())
            .sectionsData(toJsonString(gbgChecklist.getSections()))
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .build();
    }

    /**
     * Convert GbgChecklist entity to ChecklistResponseDto
     */
    public ChecklistResponseDto toChecklistResponseDto(GbgChecklist checklist) {
        List<GbgChecklistSection> sections = fromJsonString(checklist.getSectionsData(), 
            objectMapper.getTypeFactory().constructCollectionType(List.class, GbgChecklistSection.class));
        
        return ChecklistResponseDto.builder()
            .profileId(checklist.getGbgProfile().getGbgProfileUuid())
            .checklistId(checklist.getGbgChecklistId())
            .name(checklist.getName())
            .description("Compliance checklist for " + checklist.getName())
            .uncompletedTasks(checklist.getUncompletedTasks())
            .completedTasks(checklist.getCompletedTasks())
            .totalTasks(checklist.getTotalTasks())
            .completionPercentage(checklist.getCompletionPercentage())
            .overallStatus(checklist.getOverallStatus())
            .createdAt(checklist.getCreatedAt())
            .updatedAt(checklist.getUpdatedAt())
            .sections(mapChecklistSections(sections))
            .complianceSummary(buildComplianceSummary(checklist))
            .build();
    }

    /**
     * Calculate risk summary from multiple analyses
     */
    public AnalysisResponseDto.RiskSummaryDto calculateRiskSummary(List<AnalysisResponseDto> analyses) {
        if (analyses.isEmpty()) {
            return AnalysisResponseDto.RiskSummaryDto.builder()
                .totalScore(0)
                .riskCategory("Unknown")
                .requiresManualReview(true)
                .reviewReason("No analysis data available")
                .build();
        }

        // Get the latest analysis
        AnalysisResponseDto latestAnalysis = analyses.get(0);
        
        // Calculate average score
        double avgScore = analyses.stream()
            .filter(a -> a.getScore() != null)
            .mapToInt(AnalysisResponseDto::getScore)
            .average()
            .orElse(0.0);

        // Determine risk category
        String riskCategory = determineRiskCategory((int) avgScore);
        
        return AnalysisResponseDto.RiskSummaryDto.builder()
            .totalScore((int) avgScore)
            .riskCategory(riskCategory)
            .riskLevel(latestAnalysis.getRiskLevel())
            .requiresManualReview(avgScore >= 70 || "High".equals(riskCategory))
            .reviewReason(avgScore >= 70 ? "High risk score requires manual review" : null)
            .riskFactors(extractRiskFactors(analyses))
            .recommendations(generateRecommendations(riskCategory, avgScore))
            .build();
    }

    /**
     * Calculate compliance summary from checklists
     */
    public ChecklistResponseDto.ComplianceSummaryDto calculateComplianceSummary(List<ChecklistResponseDto> checklists) {
        if (checklists.isEmpty()) {
            return ChecklistResponseDto.ComplianceSummaryDto.builder()
                .isCompliant(false)
                .complianceStatus("incomplete")
                .complianceScore(0)
                .missingRequirements(List.of("No checklists available"))
                .build();
        }

        // Calculate overall compliance
        long completedChecklists = checklists.stream()
            .filter(c -> "passed".equals(c.getOverallStatus()))
            .count();
        
        double compliancePercentage = (double) completedChecklists / checklists.size() * 100;
        boolean isCompliant = compliancePercentage >= 100.0;
        
        // Collect missing requirements
        List<String> missingRequirements = checklists.stream()
            .filter(c -> !"passed".equals(c.getOverallStatus()))
            .map(ChecklistResponseDto::getName)
            .collect(Collectors.toList());

        return ChecklistResponseDto.ComplianceSummaryDto.builder()
            .isCompliant(isCompliant)
            .complianceStatus(determineComplianceStatus(compliancePercentage))
            .complianceScore((int) compliancePercentage)
            .missingRequirements(missingRequirements)
            .failedChecks(extractFailedChecks(checklists))
            .pendingActions(extractPendingActions(checklists))
            .lastReviewDate(getLatestReviewDate(checklists))
            .issues(extractComplianceIssues(checklists))
            .build();
    }

    // Helper methods

    private GbgAnalysisResult.AnalysisStatus mapAnalysisStatus(String status) {
        try {
            return GbgAnalysisResult.AnalysisStatus.fromValue(status);
        } catch (Exception e) {
            log.atWarn().log("Unknown analysis status: {}, defaulting to PENDING", status);
            return GbgAnalysisResult.AnalysisStatus.PENDING;
        }
    }

    private GbgAnalysisResult.RiskLevel mapRiskLevel(String risk) {
        try {
            return GbgAnalysisResult.RiskLevel.fromDisplayName(risk);
        } catch (Exception e) {
            log.atWarn().log("Unknown risk level: {}, defaulting to MEDIUM", risk);
            return GbgAnalysisResult.RiskLevel.MEDIUM;
        }
    }

    private String generateAnalysisId(GbgAnalysisResponse gbgAnalysis) {
        return "analysis_" + System.currentTimeMillis() + "_" + 
               (gbgAnalysis.getScore() != null ? gbgAnalysis.getScore() : "unknown");
    }

    private LocalDateTime parseDateTime(String dateTimeStr) {
        try {
            return LocalDateTime.parse(dateTimeStr);
        } catch (Exception e) {
            log.atWarn().log("Failed to parse datetime: {}, using current time", dateTimeStr);
            return LocalDateTime.now();
        }
    }

    private List<AnalysisResponseDto.AnalysisReportDto> mapAnalysisReports(GbgAnalysisResponse gbgAnalysis) {
        if (gbgAnalysis == null || gbgAnalysis.getReport() == null) {
            return List.of();
        }
        
        return gbgAnalysis.getReport().stream()
            .map(this::mapAnalysisReport)
            .collect(Collectors.toList());
    }

    private AnalysisResponseDto.AnalysisReportDto mapAnalysisReport(GbgAnalysisReport gbgReport) {
        return AnalysisResponseDto.AnalysisReportDto.builder()
            .name(gbgReport.getName())
            .description("Analysis report for " + gbgReport.getName())
            .results(mapAnalysisResults(gbgReport.getResult()))
            .overallResult(calculateOverallResult(gbgReport.getResult()))
            .overallScore(calculateOverallScore(gbgReport.getResult()))
            .build();
    }

    private List<AnalysisResponseDto.AnalysisResultDto> mapAnalysisResults(List<GbgAnalysisResult> gbgResults) {
        if (gbgResults == null) {
            return List.of();
        }
        
        return gbgResults.stream()
            .map(this::mapAnalysisResult)
            .collect(Collectors.toList());
    }

    private AnalysisResponseDto.AnalysisResultDto mapAnalysisResult(GbgAnalysisResult gbgResult) {
        return AnalysisResponseDto.AnalysisResultDto.builder()
            .id(gbgResult.getId())
            .label(gbgResult.getLabel())
            .description(gbgResult.getDescription())
            .score(gbgResult.getScore())
            .state(gbgResult.getState())
            .result(gbgResult.getResult())
            .details(gbgResult.getDescription())
            .recommendations(generateResultRecommendations(gbgResult))
            .build();
    }

    private AnalysisResponseDto.RiskSummaryDto buildRiskSummary(GbgAnalysisResult analysisResult) {
        return AnalysisResponseDto.RiskSummaryDto.builder()
            .totalScore(analysisResult.getScore())
            .riskCategory(analysisResult.getRiskLevel() != null ? analysisResult.getRiskLevel().getDisplayName() : "Unknown")
            .riskLevel(analysisResult.getRiskLevel() != null ? analysisResult.getRiskLevel().getDisplayName() : null)
            .requiresManualReview(analysisResult.getScore() != null && analysisResult.getScore() >= 70)
            .reviewReason(analysisResult.getScore() != null && analysisResult.getScore() >= 70 ? 
                "High risk score requires manual review" : null)
            .build();
    }

    private List<ChecklistResponseDto.ChecklistSectionDto> mapChecklistSections(List<GbgChecklistSection> sections) {
        if (sections == null) {
            return List.of();
        }
        
        return sections.stream()
            .map(this::mapChecklistSection)
            .collect(Collectors.toList());
    }

    private ChecklistResponseDto.ChecklistSectionDto mapChecklistSection(GbgChecklistSection section) {
        return ChecklistResponseDto.ChecklistSectionDto.builder()
            .sectionId(section.getId())
            .name(section.getName())
            .description("Section: " + section.getName())
            .status(section.getStatus())
            .totalComments(section.getTotalComments())
            .memberBelongsToTeam(section.getMemberBelongsToTeam())
            .tasks(mapChecklistTasks(section.getTasks()))
            .build();
    }

    private List<ChecklistResponseDto.ChecklistTaskDto> mapChecklistTasks(List<GbgChecklistTask> tasks) {
        if (tasks == null) {
            return List.of();
        }
        
        return tasks.stream()
            .map(this::mapChecklistTask)
            .collect(Collectors.toList());
    }

    private ChecklistResponseDto.ChecklistTaskDto mapChecklistTask(GbgChecklistTask task) {
        return ChecklistResponseDto.ChecklistTaskDto.builder()
            .taskId(task.getId())
            .name(task.getName())
            .description("Task: " + task.getName())
            .status("pending") // Default status
            .assignedTeam(task.getTeam() != null ? task.getTeam().getLabel() : null)
            .teamId(task.getTeam() != null ? task.getTeam().getId() : null)
            .build();
    }

    private ChecklistResponseDto.ComplianceSummaryDto buildComplianceSummary(GbgChecklist checklist) {
        boolean isCompliant = checklist.isCompleted();
        
        return ChecklistResponseDto.ComplianceSummaryDto.builder()
            .isCompliant(isCompliant)
            .complianceStatus(isCompliant ? "compliant" : "non_compliant")
            .complianceScore(checklist.getCompletionPercentage().intValue())
            .lastReviewDate(checklist.getUpdatedAt())
            .build();
    }

    // Utility methods

    private String toJsonString(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.atError().withCause(e).log("Failed to convert object to JSON string");
            return "{}";
        }
    }

    private <T> T fromJsonString(String json, Class<T> clazz) {
        try {
            return objectMapper.readValue(json, clazz);
        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to parse JSON string to object");
            return null;
        }
    }

    private <T> T fromJsonString(String json, com.fasterxml.jackson.core.type.TypeReference<T> typeRef) {
        try {
            return objectMapper.readValue(json, typeRef);
        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to parse JSON string to object");
            return null;
        }
    }

    private String determineRiskCategory(int score) {
        if (score >= 80) return "High";
        if (score >= 50) return "Medium";
        return "Low";
    }

    private String determineComplianceStatus(double percentage) {
        if (percentage >= 100.0) return "compliant";
        if (percentage >= 80.0) return "pending_review";
        if (percentage >= 50.0) return "incomplete";
        return "non_compliant";
    }

    private String calculateOverallResult(List<GbgAnalysisResult> results) {
        if (results == null || results.isEmpty()) return "unknown";
        
        long passCount = results.stream()
            .filter(r -> "pass".equals(r.getResult()))
            .count();
        
        return passCount > results.size() / 2 ? "pass" : "fail";
    }

    private Integer calculateOverallScore(List<GbgAnalysisResult> results) {
        if (results == null || results.isEmpty()) return 0;
        
        return (int) results.stream()
            .filter(r -> r.getScore() != null)
            .mapToInt(GbgAnalysisResult::getScore)
            .average()
            .orElse(0.0);
    }

    private List<String> generateResultRecommendations(GbgAnalysisResult result) {
        // Generate recommendations based on result
        return List.of("Review " + result.getLabel() + " for compliance");
    }

    private List<AnalysisResponseDto.RiskFactorDto> extractRiskFactors(List<AnalysisResponseDto> analyses) {
        // Extract risk factors from analyses
        return List.of();
    }

    private List<String> generateRecommendations(String riskCategory, double score) {
        if ("High".equals(riskCategory)) {
            return List.of("Immediate manual review required", "Additional documentation needed");
        } else if ("Medium".equals(riskCategory)) {
            return List.of("Enhanced due diligence recommended", "Monitor for changes");
        }
        return List.of("Standard monitoring sufficient");
    }

    private List<String> extractFailedChecks(List<ChecklistResponseDto> checklists) {
        return checklists.stream()
            .filter(c -> "failed".equals(c.getOverallStatus()))
            .map(ChecklistResponseDto::getName)
            .collect(Collectors.toList());
    }

    private List<String> extractPendingActions(List<ChecklistResponseDto> checklists) {
        return checklists.stream()
            .filter(c -> "needs_review".equals(c.getOverallStatus()))
            .map(c -> "Review " + c.getName())
            .collect(Collectors.toList());
    }

    private LocalDateTime getLatestReviewDate(List<ChecklistResponseDto> checklists) {
        return checklists.stream()
            .map(ChecklistResponseDto::getUpdatedAt)
            .max(LocalDateTime::compareTo)
            .orElse(null);
    }

    private List<ChecklistResponseDto.ComplianceIssueDto> extractComplianceIssues(List<ChecklistResponseDto> checklists) {
        return checklists.stream()
            .filter(c -> !"passed".equals(c.getOverallStatus()))
            .map(c -> ChecklistResponseDto.ComplianceIssueDto.builder()
                .issueType("major")
                .category("compliance")
                .description("Checklist " + c.getName() + " is not completed")
                .status("open")
                .identifiedAt(c.getCreatedAt())
                .build())
            .collect(Collectors.toList());
    }
}
