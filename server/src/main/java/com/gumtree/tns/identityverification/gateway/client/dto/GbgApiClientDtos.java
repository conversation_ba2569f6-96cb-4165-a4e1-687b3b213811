package com.gumtree.tns.identityverification.gateway.client.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.List;

/**
 * Collection of GBG API Client DTOs based on GBG-detected API v2
 */
public class GbgApiClientDtos {

    // ========== Search API DTOs ==========

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgSearchRequest {
        private String type; // "company"
        private String name;
        private String vatNumber;
        private String companyReference;
        private GbgAddress address;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgAddress {
        private String line1;
        private String line2;
        private String countryCode; // ISO 3166-1 alpha-2
        private String town;
        private String city;
        private String postCode;
        private String province;
        private String state;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgSearchResponse {
        private List<GbgSearchResult> data;
        private Integer lookupId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgSearchResult {
        private String type;
        private String name;
        private String companyReference;
        private String provider;
        private String providerReference;
        private String jurisdictionCode;
        private String companyType;
        private String currentStatus;
        private GbgAddress address;
        private Integer responseId;
    }

    // ========== Profile API DTOs ==========

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgCreateProfileRequest {
        private Integer lookupId; // From search results
        private Integer responseId; // From search results
        private String customerReference;
        private String companyNumber;
        private String name;
        private String vatNumber;
        private String domain;
        private GbgAddress address;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgProfileUpdateRequest {
        private String assigneeId;
        private String riskId;
        private String teamId;
        private String customerReference;
        private Integer configurationId;
        private String reviewStatus; // Processed, Needs Review, Additional Info Required, Approved, Declined, Archived
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgProfileResponse {
        private String id;
        private String customerReference;
        private String companyName;
        private GbgRisk risk;
        private GbgTeam team;
        private GbgConfiguration configuration;
        private String reviewStatus;
        private List<GbgOnboardingStatus> onboardingStatuses;
        private GbgAssignee assignee;
        private String reviewAt;
        private GbgCompany company;
        private LocalDateTime updatedAt;
        private LocalDateTime createdAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgRisk {
        private String id;
        private String label;
        private String category; // Low, Medium, High
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgTeam {
        private Integer id;
        private String label;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgConfiguration {
        private Integer id;
        private String name;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgOnboardingStatus {
        private String title;
        private String status; // pending, completed, in-progress, in-complete
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgAssignee {
        private String id;
        private String name;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgCompany {
        private Map<String, Object> profile;
        private GbgDirectorInfo director;
        private GbgShareStructure shareStructure;
        private GbgStructure structure;
        private GbgFinancials financials;
        private List<GbgPsc> psc;
        private List<GbgDomain> domain;
        private List<Object> screenings;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgDirectorInfo {
        private List<GbgDirector> data;
        private GbgDirectorSummary summary;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgDirector {
        private String directorId;
        private String jobTitle;
        private String gender;
        private String appointmentDate;
        private String resignationDate;
        private String dob;
        private String address;
        private String country;
        private String nationality;
        private String companyNumber;
        private List<Object> screenings;
        private Boolean isActive;
        private String suffix;
        private String salutation;
        private String shareholder;
        private String positionType;
        private String responsibility;
        private String type;
        private String title;
        private String fullName;
        private String firstName;
        private String lastName;
        private String middleName;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgDirectorSummary {
        private Integer totalCount;
        private Integer activeCount;
        private Integer inactiveCount;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgShareStructure {
        private GbgShareData data;
        private Integer issuedSharesTotal;
        private Integer issuedSharesCapitalValue;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgShareData {
        private List<GbgShareholder> shareHolders;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgShareholder {
        private String id;
        private String name;
        private String currency;
        private String shareType;
        private List<GbgShareType> shareTypes;
        private Integer valueOfShares;
        private Integer numberOfShares;
        private String shareholderType;
        private Double percentageSharesHeld;
        private String address;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgShareType {
        private String currency;
        private String shareType;
        private Integer valueOfShares;
        private Double valuePerShare;
        private Integer numberOfShares;
        private String companyReference;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgStructure {
        private GbgParent ultimateParent;
        private GbgParent immediateParent;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgParent {
        private String providerId;
        private String name;
        private String countryCode;
        private String type;
        private String status;
        private String companyReference;
        private String address;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgFinancials {
        private List<GbgFinancialData> data;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgFinancialData {
        private GbgRatios ratios;
        private GbgGeneral general;
        private GbgBalanceSheet balanceSheet;
        private GbgProfitAndLoss profitAndLoss;
        private GbgOtherFinancials otherFinancials;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgRatios {
        private Double gearing;
        private Double debtorDays;
        private Double creditorDays;
        private Double currentRatio;
        private Double totalDebtRatio;
        private Double currentDebtRatio;
        private Double equityInPercentage;
        private Double stockTurnoverRatio;
        private Double preTaxProfitMargin;
        private Double returnOnCapitalEmployed;
        private Double liquidityRatioOrAcidTest;
        private Double salesOrNetWorkingCapital;
        private Double returnOnNetAssetsEmployed;
        private Double returnOnTotalAssetsEmployed;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgGeneral {
        private String type;
        private String currency;
        private String yearEndDate;
        private Integer numberOfWeeks;
        private Boolean consolidatedAccounts;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgBalanceSheet {
        private Long cash;
        private Long totalAssets;
        private Long otherReserves;
        private Long tradePayables;
        private Long bankLiabilities;
        private Long revenueReserves;
        private Long totalInventories;
        private Long totalLiabilities;
        private Long totalReceivables;
        private Long tradeReceivables;
        private Long totalFixedAssets;
        private Long otherCurrentAssets;
        private Long totalCurrentAssets;
        private Long totalTangibleAssets;
        private Long otherLoansOrFinance;
        private Long calledUpShareCapital;
        private Long totalIntangibleAssets;
        private Long totalOtherFixedAssets;
        private Long miscellaneousLiabilities;
        private Long miscellaneousReceivables;
        private Long totalCurrentLiabilities;
        private Long totalShareholdersEquity;
        private Long totalLongTermLiabilities;
        private Long bankLiabilitiesDueAfter1Year;
        private Long otherLoansOrFinanceDueAfter1Year;
        private Long miscellaneousLiabilitiesDueAfter1Year;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgProfitAndLoss {
        private Long tax;
        private Long revenue;
        private Long dividends;
        private Long amortisation;
        private Long depreciation;
        private Long pensionCosts;
        private Long operatingCosts;
        private Long retainedProfit;
        private Long operatingProfit;
        private Long profitAfterTax;
        private Long profitBeforeTax;
        private Long financialExpenses;
        private Long minorityInterests;
        private Long wagesAndSalaries;
        private Long otherAppropriations;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgOtherFinancials {
        private Long netWorth;
        private Long workingCapital;
        private String contingentLiabilities;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgPsc {
        private List<String> natureOfControl;
        private Boolean isActive;
        private String name;
        private String kind;
        private String entityType;
        private String address;
        private String notifiedOn;
        private String ceasedOn;
        private Integer dobDay;
        private Integer dobMonth;
        private Integer dobYear;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgDomain {
        private String domain;
        private List<GbgOnlinePresence> onlinePresence;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgOnlinePresence {
        private String foundingYear;
        private String phoneNumber;
        private String industry;
        private List<String> tags;
        private String address;
    }

    // ========== Representatives API DTOs ==========

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgCreateRepresentativeRequest {
        private GbgRepresentativeName name;
        private String email;
        private String profileId;
        private List<String> types; // director, identity, ownership, declaratory, principal, director_company
        private String dob; // yyyy-mm-dd format
        private String professionalProfile;
        private String jobTitle;
        private String residenceCountryCode;
        private String birthCountryCode;
        private String nationality;
        private GbgAddress address;
        private String personalNumber;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgRepresentativeName {
        private String firstName;
        private String middleName;
        private String lastName;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgRepresentativeResponse {
        private String id;
        private String profileId;
        private String customerReference;
        private Boolean isCompany;
        private List<String> types;
        private String fullName;
        private String professionalProfile;
        private String country;
        private String countryCode;
        private String countryOfResidence;
        private String residenceCountryCode;
        private String personalNumber;
        private String firstName;
        private String lastName;
        private String dob;
        private String email;
        private String jobTitle;
        private String apartmentNumber;
        private String address1;
        private String address2;
        private String city;
        private String state;
        private String postCode;
        private String countryOfBirth;
        private String birthCountryCode;
        private String nationality;
        private GbgPersonTypeDetails personTypeDetails;
        private Boolean copyAddress;
        private LocalDateTime updatedAt;
        private LocalDateTime createdAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgPersonTypeDetails {
        private String appointmentDate;
        private Boolean majority;
        private Boolean governing;
        private Boolean responsibility;
        private String majorityPercentage;
        private String specificOwnershipPercentage;
    }

    // ========== Verifications API DTOs ==========

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgVerificationResponse {
        private String type; // idv, id3, digital_footprint
        private String provider; // id_scan, yoti, risk_seal, id3
        private Map<String, Object> info; // Provider-specific verification details
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgIdvVerificationInfo {
        private String sessionId;
        private String sessionToken;
        private String iframeUrl;
        private String qrCode;
        private String state; // open, closed
        private GbgVerificationResult result;
        private String createdAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgVerificationResult {
        private String name;
        private String state; // COMPLETE, PENDING, FAILED
        private String identityStatus; // APPROVED, REJECTED, PENDING
        private String identityFailReason;
        private String livenessStatus; // APPROVED, REJECTED, NOT-PERFORMED-UPLOAD
        private String faceMatchStatus; // APPROVED, REJECTED, NOT-PERFORMED-UPLOAD
        private String faceMatchFailReason;
        private String idRequestedAt;
        private String dobOnDocument;
        private String expirationDate;
        private String expirationStatus;
        private String journeyId;
        private String provider;
        private Boolean canDownloadReport;
        private String canDownloadUntil;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgDigitalFootprintInfo {
        private String name;
        private String phoneNumber;
        private Map<String, String> phoneStatuses;
        private String emailAddress;
        private Map<String, String> emailStatuses;
        private List<String> photos;
        private String emailAge;
        private String country;
        private String gender;
        private String trustScore;
        private Boolean suspicious;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgId3VerificationInfo {
        private Integer id;
        private GbgId3Config config;
        private String personUuid;
        private Map<String, Object> request;
        private List<Map<String, Object>> response;
        private String status; // complete, pending, failed
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgId3Config {
        private Integer id;
        private String name;
        private String description;
        private String id3ProfileId;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
    }

    // ========== Documents API DTOs ==========

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgDocumentListResponse {
        private Integer id;
        private String profileId;
        private String libraryId;
        private String category;
        private String name;
        private LocalDateTime updatedAt;
        private LocalDateTime createdAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgDocumentViewResponse {
        private Integer id;
        private String profileId;
        private String documentId;
        private String category;
        private String name;
        private String link;
        private String fileType;
        private String linkExpiresAt;
        private LocalDateTime updatedAt;
        private LocalDateTime createdAt;
    }

    // ========== Analysis API DTOs ==========

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgAnalysisResponse {
        private Integer score;
        private String status; // failed, completed, pending, missing_data
        private String risk; // Low, Medium, High
        private String createdAt;
        private List<GbgAnalysisReport> report;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgAnalysisReport {
        private String name;
        private List<GbgAnalysisResult> result;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgAnalysisResult {
        private String id;
        private Integer score;
        private String state; // skipped, analysed, failed
        private String label;
        private String description;
        private String result;
    }

    // ========== Checklists API DTOs ==========

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgChecklistResponse {
        private Integer id;
        private String name;
        private List<GbgChecklistSection> sections;
        private Integer uncompletedTasks;
        private Integer completedTasks;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgChecklistSection {
        private Integer id;
        private String name;
        private List<GbgChecklistTask> tasks;
        private String status; // exempt, failed, passed, needs_review
        private Integer totalComments;
        private Boolean memberBelongsToTeam;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgChecklistTask {
        private Integer id;
        private String name;
        private GbgTeam team;
    }

    // ========== Forms API DTOs ==========

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgFormResponse {
        private Integer id;
        private String label;
        private String reference;
        private List<GbgFormAnswer> answers;
        private LocalDateTime updatedAt;
        private LocalDateTime createdAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgFormAnswer {
        private String label;
        private String answer;
    }

    // ========== Portal Users API DTOs ==========

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgCreatePortalUserRequest {
        private String email;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgAssignPortalUserRequest {
        private Integer userId;
        private String role; // admin
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgPortalUserResponse {
        private Integer id;
        private String email;
        private String name;
        private String profileId;
        private String role;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgPortalUserActivityResponse {
        private List<GbgPortalActivity> data;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgPortalActivity {
        private String id;
        private String userId;
        private String profileId;
        private String email;
        private String ip;
        private String type; // access_log
        private String name;
        private Map<String, Object> details;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
    }

    // ========== Domain API DTOs ==========

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgCreateDomainRequest {
        private String domain;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgDomainResponse {
        private String id;
        private String type; // portal or api_import
        private String domain;
        private GbgWhois whois;
        private GbgSocial social;
        private List<GbgOnlinePresence> onlinePresence;
        private GbgScamAdvisor scamAdvisor;
        private LocalDateTime updatedAt;
        private LocalDateTime createdAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgWhois {
        private String status;
        private String age;
        private Integer ageDays;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
        private LocalDateTime expiresAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgSocial {
        private String title;
        private String description;
        private Boolean responded;
        private String twitter;
        private String facebook;
        private String linkedIn;
        private String instagram;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgScamAdvisor {
        private Integer score;
        private Boolean blacklisted;
        private Integer dnsThreatMarkers;
        private Integer dnsAdultContentMarkers;
        private String websiteTitle;
        private String websiteDescription;
        private List<String> websiteKeywords;
        private Boolean blockSearchEngines;
        private List<GbgReview> reviews;
        private GbgGcaDomainTrust gcaDomainTrust;
        private GbgAntiphishing antiphishing;
        private GbgCertNz certNz;
        private GbgMaltiverse maltiverse;
        private GbgIpqs ipqs;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgReview {
        private String source;
        private String sourceUrl;
        private Integer count;
        private Double avgScore;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgGcaDomainTrust {
        private String type;
        private String activity;
        private String provider;
        private LocalDateTime createdAt;
        private Boolean isBlocked;
        private LocalDateTime updatedAt;
        private String sourceName;
        private String providerRole;
        private Integer classification;
        private Boolean externalSource;
        private String providerRating;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgAntiphishing {
        private Integer count;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgCertNz {
        private List<String> labels;
        private String marking;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgMaltiverse {
        private List<String> tags;
        private String classification;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgIpqs {
        private Integer riskScore;
        private Boolean unsafe;
        private Boolean dnsValid;
        private Boolean parking;
        private Boolean spamming;
        private Boolean malware;
        private Boolean phishing;
        private Boolean suspicious;
        private Boolean adult;
        private Long domainAge;
    }

    // ========== Common Response DTOs ==========

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgApiResponse<T> {
        private T data;
        private GbgLinks links;
        private GbgMeta meta;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgLinks {
        private String first;
        private String last;
        private String prev;
        private String next;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgMeta {
        private Integer currentPage;
        private Integer from;
        private Integer lastPage;
        private List<GbgMetaLink> links;
        private String path;
        private Integer perPage;
        private Integer to;
        private Integer total;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgMetaLink {
        private String url;
        private String label;
        private Boolean active;
    }

    // ========== Error Response DTOs ==========

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgErrorResponse {
        private String message;
        private Integer status;
        private String error;
        private String timestamp;
        private String path;
        private Map<String, List<String>> errors;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgApiDomainResponse {
        private String domain;
        private String status;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgApiAnalysisResponse {
        private String analysisType;
        private String result;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgApiChecklistResponse {
        private String checklistName;
        private String status;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GbgApiFormResponse {
        private String formName;
        private String status;
    }
}
