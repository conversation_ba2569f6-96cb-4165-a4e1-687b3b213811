package com.gumtree.tns.identityverification.dao.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Entity representing GBG Representative Verification
 */
@Entity
@Table(name = "gbg_representative_verifications")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GbgRepresentativeVerification {

    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", updatable = false, nullable = false)
    private UUID id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "gbg_representative_id", nullable = false)
    private GbgRepresentative gbgRepresentative;

    @Enumerated(EnumType.STRING)
    @Column(name = "verification_type", nullable = false)
    private VerificationType verificationType;

    @Enumerated(EnumType.STRING)
    @Column(name = "provider", nullable = false)
    private Provider provider;

    @Column(name = "session_id")
    private String sessionId;

    @Column(name = "session_token")
    private String sessionToken;

    @Column(name = "iframe_url", length = 1000)
    private String iframeUrl;

    @Column(name = "qr_code", length = 1000)
    private String qrCode;

    @Enumerated(EnumType.STRING)
    @Column(name = "state", nullable = false)
    private VerificationState state;

    @Type(type = "jsonb")
    @Column(name = "verification_result", columnDefinition = "jsonb")
    private String verificationResult;

    @Type(type = "jsonb")
    @Column(name = "provider_data", columnDefinition = "jsonb")
    private String providerData;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        if (createdAt == null) {
            createdAt = now;
        }
        if (updatedAt == null) {
            updatedAt = now;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    /**
     * Verification types supported by GBG
     */
    public enum VerificationType {
        IDV("idv"),           // Identity verification
        ID3("id3"),           // ID3 verification
        DIGITAL_FOOTPRINT("digital_footprint"); // Digital footprint verification

        private final String value;

        VerificationType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static VerificationType fromValue(String value) {
            for (VerificationType type : values()) {
                if (type.value.equals(value)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("Unknown verification type: " + value);
        }
    }

    /**
     * Verification providers supported by GBG
     */
    public enum Provider {
        ID_SCAN("id_scan"),
        YOTI("yoti"),
        RISK_SEAL("risk_seal"),
        ID3("id3");

        private final String value;

        Provider(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static Provider fromValue(String value) {
            for (Provider provider : values()) {
                if (provider.value.equals(value)) {
                    return provider;
                }
            }
            throw new IllegalArgumentException("Unknown provider: " + value);
        }
    }

    /**
     * Verification states
     */
    public enum VerificationState {
        OPEN("open"),
        CLOSED("closed"),
        PENDING("pending"),
        FAILED("failed");

        private final String value;

        VerificationState(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static VerificationState fromValue(String value) {
            for (VerificationState state : values()) {
                if (state.value.equals(value)) {
                    return state;
                }
            }
            throw new IllegalArgumentException("Unknown verification state: " + value);
        }
    }
}
