package com.gumtree.tns.identityverification.dao.repository;

import com.gumtree.tns.identityverification.dao.model.GbgAnalysisResult;
import com.gumtree.tns.identityverification.dao.model.GbgProfile;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository for GBG Analysis Result operations
 */
@Repository
public interface GbgAnalysisResultRepository extends JpaRepository<GbgAnalysisResult, UUID> {

    /**
     * Find analysis results by profile
     */
    List<GbgAnalysisResult> findByGbgProfileOrderByCreatedAtDesc(GbgProfile gbgProfile);

    /**
     * Find analysis results by profile UUID
     */
    @Query("SELECT a FROM GbgAnalysisResult a WHERE a.gbgProfile.gbgProfileUuid = :profileUuid ORDER BY a.createdAt DESC")
    List<GbgAnalysisResult> findByProfileUuidOrderByCreatedAtDesc(@Param("profileUuid") String profileUuid);

    /**
     * Find latest analysis result by profile
     */
    @Query("SELECT a FROM GbgAnalysisResult a WHERE a.gbgProfile = :profile ORDER BY a.createdAt DESC")
    Optional<GbgAnalysisResult> findLatestByProfile(@Param("profile") GbgProfile profile);

    /**
     * Find analysis results by status
     */
    List<GbgAnalysisResult> findByStatusOrderByCreatedAtDesc(GbgAnalysisResult.AnalysisStatus status);

    /**
     * Find analysis results by risk level
     */
    List<GbgAnalysisResult> findByRiskLevelOrderByCreatedAtDesc(GbgAnalysisResult.RiskLevel riskLevel);

    /**
     * Find analysis results by profile and status
     */
    List<GbgAnalysisResult> findByGbgProfileAndStatusOrderByCreatedAtDesc(
        GbgProfile gbgProfile, GbgAnalysisResult.AnalysisStatus status);

    /**
     * Find analysis results by profile and risk level
     */
    List<GbgAnalysisResult> findByGbgProfileAndRiskLevelOrderByCreatedAtDesc(
        GbgProfile gbgProfile, GbgAnalysisResult.RiskLevel riskLevel);

    /**
     * Find analysis results by analysis ID
     */
    Optional<GbgAnalysisResult> findByAnalysisId(String analysisId);

    /**
     * Find analysis results with score above threshold
     */
    @Query("SELECT a FROM GbgAnalysisResult a WHERE a.score >= :minScore ORDER BY a.score DESC, a.createdAt DESC")
    List<GbgAnalysisResult> findByScoreGreaterThanEqualOrderByScoreDescCreatedAtDesc(@Param("minScore") Integer minScore);

    /**
     * Find analysis results with score below threshold
     */
    @Query("SELECT a FROM GbgAnalysisResult a WHERE a.score < :maxScore ORDER BY a.score ASC, a.createdAt DESC")
    List<GbgAnalysisResult> findByScoreLessThanOrderByScoreAscCreatedAtDesc(@Param("maxScore") Integer maxScore);

    /**
     * Find analysis results created within date range
     */
    @Query("SELECT a FROM GbgAnalysisResult a WHERE a.createdAt >= :startDate AND a.createdAt <= :endDate ORDER BY a.createdAt DESC")
    List<GbgAnalysisResult> findByCreatedAtBetweenOrderByCreatedAtDesc(
        @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * Find top N analysis results by profile
     */
    @Query(value = "SELECT * FROM gbg_analysis_results WHERE gbg_profile_id = :#{#profile.id} ORDER BY created_at DESC LIMIT :limit", 
           nativeQuery = true)
    List<GbgAnalysisResult> findTopNByGbgProfileOrderByCreatedAtDesc(@Param("profile") GbgProfile profile, @Param("limit") Integer limit);

    /**
     * Count analysis results by profile
     */
    Long countByGbgProfile(GbgProfile gbgProfile);

    /**
     * Count analysis results by status
     */
    Long countByStatus(GbgAnalysisResult.AnalysisStatus status);

    /**
     * Count analysis results by risk level
     */
    Long countByRiskLevel(GbgAnalysisResult.RiskLevel riskLevel);

    /**
     * Find analysis results that need attention (failed or pending for too long)
     */
    @Query("SELECT a FROM GbgAnalysisResult a WHERE " +
           "(a.status = 'FAILED') OR " +
           "(a.status = 'PENDING' AND a.createdAt < :cutoffTime) " +
           "ORDER BY a.createdAt ASC")
    List<GbgAnalysisResult> findAnalysisNeedingAttention(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * Get average score by risk level
     */
    @Query("SELECT AVG(a.score) FROM GbgAnalysisResult a WHERE a.riskLevel = :riskLevel AND a.score IS NOT NULL")
    Double getAverageScoreByRiskLevel(@Param("riskLevel") GbgAnalysisResult.RiskLevel riskLevel);

    /**
     * Find profiles with high risk analysis
     */
    @Query("SELECT DISTINCT a.gbgProfile FROM GbgAnalysisResult a WHERE a.riskLevel = 'HIGH' ORDER BY a.createdAt DESC")
    List<GbgProfile> findProfilesWithHighRisk();

    /**
     * Check if profile has any completed analysis
     */
    @Query("SELECT CASE WHEN COUNT(a) > 0 THEN true ELSE false END FROM GbgAnalysisResult a WHERE " +
           "a.gbgProfile = :profile AND a.status = 'COMPLETED'")
    boolean hasCompletedAnalysis(@Param("profile") GbgProfile profile);

    /**
     * Find most recent analysis by profile and status
     */
    @Query("SELECT a FROM GbgAnalysisResult a WHERE a.gbgProfile = :profile AND a.status = :status ORDER BY a.createdAt DESC")
    Optional<GbgAnalysisResult> findMostRecentByProfileAndStatus(
        @Param("profile") GbgProfile profile, @Param("status") GbgAnalysisResult.AnalysisStatus status);

    /**
     * Delete old analysis results (cleanup)
     */
    @Query("DELETE FROM GbgAnalysisResult a WHERE a.createdAt < :cutoffDate")
    void deleteOldAnalysisResults(@Param("cutoffDate") LocalDateTime cutoffDate);
}
