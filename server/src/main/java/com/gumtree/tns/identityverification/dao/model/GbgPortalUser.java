package com.gumtree.tns.identityverification.dao.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Entity representing GBG Portal Users
 */
@Entity
@Table(name = "gbg_portal_users")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GbgPortalUser {

    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", updatable = false, nullable = false)
    private UUID id;

    @Column(name = "gbg_user_id", nullable = false, unique = true)
    private Integer gbgUserId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "gbg_profile_id")
    private GbgProfile gbgProfile;

    @Column(name = "email", nullable = false)
    private String email;

    @Column(name = "name")
    private String name;

    @Enumerated(EnumType.STRING)
    @Column(name = "role", nullable = false)
    private PortalUserRole role;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // Additional fields for enhanced portal user management
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    @Builder.Default
    private PortalUserStatus status = PortalUserStatus.ACTIVE;

    @Column(name = "last_login_at")
    private LocalDateTime lastLoginAt;

    @Column(name = "invitation_sent_at")
    private LocalDateTime invitationSentAt;

    @Column(name = "invitation_accepted_at")
    private LocalDateTime invitationAcceptedAt;

    @Column(name = "language")
    @Builder.Default
    private String language = "en";

    @Column(name = "timezone")
    @Builder.Default
    private String timezone = "UTC";

    @Column(name = "email_notifications")
    @Builder.Default
    private Boolean emailNotifications = true;

    @Column(name = "sms_notifications")
    @Builder.Default
    private Boolean smsNotifications = false;

    @Column(name = "notification_frequency")
    @Builder.Default
    private String notificationFrequency = "immediate";

    // Relationships
    @OneToMany(mappedBy = "gbgPortalUser", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<GbgPortalUserActivity> activities;

    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        if (createdAt == null) {
            createdAt = now;
        }
        if (updatedAt == null) {
            updatedAt = now;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    /**
     * Portal user roles
     */
    public enum PortalUserRole {
        ADMIN("admin"),
        USER("user"),
        VIEWER("viewer");

        private final String value;

        PortalUserRole(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static PortalUserRole fromValue(String value) {
            for (PortalUserRole role : values()) {
                if (role.value.equals(value)) {
                    return role;
                }
            }
            throw new IllegalArgumentException("Unknown portal user role: " + value);
        }
    }

    /**
     * Portal user status
     */
    public enum PortalUserStatus {
        ACTIVE("active"),
        INACTIVE("inactive"),
        PENDING("pending"),
        SUSPENDED("suspended"),
        DELETED("deleted");

        private final String value;

        PortalUserStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static PortalUserStatus fromValue(String value) {
            for (PortalUserStatus status : values()) {
                if (status.value.equals(value)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("Unknown portal user status: " + value);
        }
    }

    /**
     * Check if user is active
     */
    public Boolean isActive() {
        return PortalUserStatus.ACTIVE.equals(status);
    }

    /**
     * Check if user has accepted invitation
     */
    public Boolean hasAcceptedInvitation() {
        return invitationAcceptedAt != null;
    }

    /**
     * Check if invitation is pending
     */
    public Boolean isInvitationPending() {
        return invitationSentAt != null && invitationAcceptedAt == null;
    }

    /**
     * Get days since last login
     */
    public Long getDaysSinceLastLogin() {
        if (lastLoginAt == null) {
            return null;
        }
        return java.time.temporal.ChronoUnit.DAYS.between(lastLoginAt.toLocalDate(), LocalDateTime.now().toLocalDate());
    }

    /**
     * Check if user can perform admin actions
     */
    public Boolean canPerformAdminActions() {
        return PortalUserRole.ADMIN.equals(role) && isActive();
    }
}
