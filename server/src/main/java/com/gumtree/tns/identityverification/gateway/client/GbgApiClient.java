package com.gumtree.tns.identityverification.gateway.client;

import com.gumtree.tns.identityverification.gateway.client.dto.GbgApiClientDtos.*;

import java.util.List;

/**
 * Client interface for GBG-detected API v2 operations
 * Based on the official GBG-detected API documentation
 */
public interface GbgApiClient {

    // ========== Search API ==========

    /**
     * Search for companies in GBG database
     * POST /search
     */
    GbgSearchResponse searchCompanies(GbgSearchRequest request);

    // ========== Profiles API ==========

    /**
     * Get list of profiles
     * GET /profiles
     */
    GbgApiResponse<List<GbgProfileResponse>> getProfiles(
        String sortBy, String orderBy, Integer page, String id, String reviewStatus,
        String teamId, String assigneeId, String riskId, String customerReference, String configurationId);

    /**
     * Create a new profile from search results or manually
     * POST /profiles
     */
    GbgApiResponse<GbgProfileResponse> createProfile(GbgCreateProfileRequest request);

    /**
     * Get profile details by UUID
     * GET /profiles/{uuid}
     */
    GbgApiResponse<GbgProfileResponse> getProfile(String profileUuid);

    /**
     * Update profile properties
     * PATCH /profiles/{uuid}
     */
    GbgApiResponse<GbgProfileResponse> updateProfile(String profileUuid, GbgProfileUpdateRequest request);

    // ========== Representatives API ==========

    /**
     * Get list of representatives
     * GET /representatives
     */
    GbgApiResponse<List<GbgRepresentativeResponse>> getRepresentatives(
        String sortBy, String orderBy, Integer page, String id, String isCompany,
        String customerReference, String profileId, String email, String types);

    /**
     * Create a new representative
     * POST /representatives
     */
    GbgApiResponse<GbgRepresentativeResponse> createRepresentative(GbgCreateRepresentativeRequest request);

    /**
     * Get representative by UUID
     * GET /representatives/{uuid}
     */
    GbgApiResponse<GbgRepresentativeResponse> getRepresentative(String representativeUuid);

    /**
     * Get verifications for a representative
     * GET /representatives/{uuid}/verifications
     */
    GbgApiResponse<GbgVerificationResponse> getRepresentativeVerifications(String representativeUuid, String type);

    // ========== Documents API ==========

    /**
     * Get list of documents
     * GET /documents
     */
    GbgApiResponse<List<GbgDocumentListResponse>> getDocuments(
        Integer id, String sortBy, String orderBy, Integer page, String profileId,
        String documentId, String category, String name);

    /**
     * Get document by ID
     * GET /documents/{id}
     */
    GbgApiResponse<GbgDocumentViewResponse> getDocument(Integer documentId);

    // ========== Analysis API ==========

    /**
     * Get profile analysis results
     * GET /profiles/{uuid}/analysis
     */
    GbgApiResponse<List<GbgAnalysisResponse>> getProfileAnalysis(String profileUuid);

    // ========== Checklists API ==========

    /**
     * Get profile checklists
     * GET /profiles/{uuid}/checklists
     */
    GbgApiResponse<List<GbgChecklistResponse>> getProfileChecklists(
        String profileUuid, String sortBy, String orderBy, Integer page, String id, String name);

    /**
     * Get profile checklist by ID
     * GET /profiles/{uuid}/checklists/{id}
     */
    GbgApiResponse<GbgChecklistResponse> getProfileChecklist(String profileUuid, String checklistId);

    // ========== Forms API ==========

    /**
     * Get profile forms
     * GET /profiles/{uuid}/forms
     */
    GbgApiResponse<List<GbgFormResponse>> getProfileForms(
        String profileUuid, String id, String label, String reference);

    /**
     * Get profile form by ID
     * GET /profiles/{uuid}/forms/{id}
     */
    GbgApiResponse<GbgFormResponse> getProfileForm(String profileUuid, String formId);

    // ========== Domain API ==========

    /**
     * Get profile domain information
     * GET /profiles/{uuid}/domain
     */
    GbgApiResponse<GbgDomainResponse> getProfileDomain(String profileUuid);

    /**
     * Create profile domain
     * POST /profiles/{uuid}/domain
     */
    void createProfileDomain(String profileUuid, GbgCreateDomainRequest request);

    // ========== Portal Users API ==========

    /**
     * Get list of portal users
     * GET /portal-users
     */
    GbgApiResponse<List<GbgPortalUserResponse>> getPortalUsers(
        String sortBy, String orderBy, Integer page, String id, String name, String email, String profileId);

    /**
     * Create a new portal user
     * POST /portal-users
     */
    GbgApiResponse<GbgPortalUserResponse> createPortalUser(GbgCreatePortalUserRequest request);

    /**
     * Get portal user by ID
     * GET /portal-users/{userId}
     */
    GbgApiResponse<GbgPortalUserResponse> getPortalUser(String userId);

    /**
     * Assign portal user to profile
     * PUT /profiles/{uuid}/portal-users
     */
    GbgApiResponse<GbgPortalUserResponse> assignPortalUserToProfile(String profileUuid, GbgAssignPortalUserRequest request);

    /**
     * Get portal user activity
     * GET /portal-users/activity
     */
    GbgApiResponse<GbgPortalUserActivityResponse> getPortalUserActivity(
        String id, String userId, String profileId, String email, String ip, String types,
        String name, String sortBy, String orderBy, Integer page);

    // ========== Users API (Case Management) ==========

    /**
     * Get list of case management users
     * GET /users
     */
    GbgApiResponse<List<GbgPortalUserResponse>> getUsers(
        String sortBy, String orderBy, Integer page, String id, String blocked, String name, String email);

    /**
     * Get case management user by ID
     * GET /users/{userId}
     */
    GbgApiResponse<GbgPortalUserResponse> getUser(String userId);
}
