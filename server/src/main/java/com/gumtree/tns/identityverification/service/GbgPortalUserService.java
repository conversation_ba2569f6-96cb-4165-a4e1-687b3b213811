package com.gumtree.tns.identityverification.service;

import com.gumtree.tns.identityverification.service.dto.PortalUserRequestDto;
import com.gumtree.tns.identityverification.service.dto.PortalUserResponseDto;
import com.gumtree.tns.identityverification.service.dto.PortalUserActivityDto;

import java.util.List;

/**
 * Service for handling GBG Portal User operations
 */
public interface GbgPortalUserService {

    /**
     * Create a new portal user
     * 
     * @param request the portal user creation request
     * @return the created portal user
     */
    PortalUserResponseDto createPortalUser(PortalUserRequestDto request);

    /**
     * Get portal user by ID
     * 
     * @param userId the portal user ID
     * @return the portal user details
     */
    PortalUserResponseDto getPortalUser(String userId);

    /**
     * Get all portal users with optional filtering
     * 
     * @param email filter by email (optional)
     * @param profileId filter by profile ID (optional)
     * @param page page number (0-based)
     * @param size page size
     * @return list of portal users
     */
    List<PortalUserResponseDto> getPortalUsers(String email, String profileId, Integer page, Integer size);

    /**
     * Assign portal user to a profile
     * 
     * @param profileUuid the GBG profile UUID
     * @param userId the portal user ID
     * @param role the role to assign (default: admin)
     * @return the updated portal user
     */
    PortalUserResponseDto assignPortalUserToProfile(String profileUuid, Integer userId, String role);

    /**
     * Update portal user information
     * 
     * @param userId the portal user ID
     * @param request the update request
     * @return the updated portal user
     */
    PortalUserResponseDto updatePortalUser(String userId, PortalUserRequestDto request);

    /**
     * Delete portal user
     * 
     * @param userId the portal user ID
     */
    void deletePortalUser(String userId);

    /**
     * Get portal user activity
     * 
     * @param userId filter by user ID (optional)
     * @param profileId filter by profile ID (optional)
     * @param activityType filter by activity type (optional)
     * @param page page number (0-based)
     * @param size page size
     * @return list of portal user activities
     */
    List<PortalUserActivityDto> getPortalUserActivity(String userId, String profileId, String activityType, 
                                                     Integer page, Integer size);

    /**
     * Get portal users by profile
     * 
     * @param profileUuid the GBG profile UUID
     * @return list of portal users for the profile
     */
    List<PortalUserResponseDto> getPortalUsersByProfile(String profileUuid);

    /**
     * Check if email is already used by a portal user
     * 
     * @param email the email to check
     * @return true if email is already used
     */
    boolean isEmailAlreadyUsed(String email);

    /**
     * Send invitation email to portal user
     * 
     * @param userId the portal user ID
     * @param profileUuid the profile UUID (optional)
     * @return true if invitation sent successfully
     */
    boolean sendInvitationEmail(String userId, String profileUuid);

    /**
     * Get portal user statistics
     * 
     * @param profileUuid filter by profile UUID (optional)
     * @return portal user statistics
     */
    PortalUserResponseDto.PortalUserStatsDto getPortalUserStatistics(String profileUuid);
}
