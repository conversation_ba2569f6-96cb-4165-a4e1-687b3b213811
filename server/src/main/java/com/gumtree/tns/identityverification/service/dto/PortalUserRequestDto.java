package com.gumtree.tns.identityverification.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;

/**
 * DTO for portal user creation/update requests
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PortalUserRequestDto {

    @Email(message = "Invalid email format")
    @NotBlank(message = "Email is required")
    private String email;

    private String name;

    private String role; // admin (default)

    private String profileId; // GBG Profile UUID to associate with

    private Boolean sendInvitation; // Whether to send invitation email

    private String language; // Preferred language for communications

    private String timezone; // User's timezone

    private PortalUserPreferencesDto preferences;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PortalUserPreferencesDto {
        private Boolean emailNotifications;
        private Boolean smsNotifications;
        private String notificationFrequency; // immediate, daily, weekly
        private String documentUploadNotifications; // all, important, none
        private String complianceUpdateNotifications; // all, important, none
    }
}
