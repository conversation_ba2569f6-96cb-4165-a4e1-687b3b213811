package com.gumtree.tns.identityverification.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * DTO for portal user activity
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PortalUserActivityDto {

    private String id;
    private String userId;
    private String profileId;
    private String email;
    private String ipAddress;
    private String userAgent;
    private String activityType; // access_log, form_submission, document_upload, profile_update
    private String activityName;
    private String description;
    private Map<String, Object> activityDetails;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private String sessionId;
    private String deviceType; // desktop, mobile, tablet
    private String browserName;
    private String operatingSystem;
    private String location; // derived from IP
    private Boolean isSuccessful;
    private String errorMessage;
    private Long durationMs; // activity duration in milliseconds

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ActivityDetailsDto {
        private String documentType;
        private String documentName;
        private Long documentSize;
        private String formType;
        private String formName;
        private Integer formCompletionPercentage;
        private String profileSection; // which part of profile was updated
        private String previousValue;
        private String newValue;
        private String changeReason;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SessionInfoDto {
        private String sessionId;
        private LocalDateTime sessionStart;
        private LocalDateTime sessionEnd;
        private Long sessionDurationMs;
        private Integer pageViews;
        private Integer actionsPerformed;
        private String entryPage;
        private String exitPage;
        private Boolean isActiveSession;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DeviceInfoDto {
        private String deviceType;
        private String browserName;
        private String browserVersion;
        private String operatingSystem;
        private String osVersion;
        private String screenResolution;
        private String language;
        private String timezone;
        private Boolean isMobile;
        private Boolean isTablet;
        private Boolean isDesktop;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LocationInfoDto {
        private String ipAddress;
        private String country;
        private String region;
        private String city;
        private String postalCode;
        private Double latitude;
        private Double longitude;
        private String timezone;
        private String isp;
        private Boolean isVpn;
        private Boolean isProxy;
    }
}
