package com.gumtree.tns.identityverification.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * DTO for workflow state information
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowStateDto {

    private String profileId;
    private String currentState;
    private String previousState;
    private String stateDescription;
    private Map<String, Object> stateData;
    private Boolean canProgress;
    private List<String> blockingReasons;
    private List<String> nextRequiredActions;
    private List<String> validNextStates;
    private LocalDateTime estimatedCompletionDate;
    private LocalDateTime stateChangedAt;
    private String stateChangedBy;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private WorkflowProgressDto progress;
    private List<WorkflowActionDto> availableActions;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WorkflowProgressDto {
        private Integer currentStep;
        private Integer totalSteps;
        private Double completionPercentage;
        private String currentStepName;
        private String nextStepName;
        private List<WorkflowStepDto> steps;
        private Long estimatedTimeRemainingMs;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WorkflowStepDto {
        private Integer stepNumber;
        private String stepName;
        private String stepDescription;
        private String status; // pending, in_progress, completed, failed, skipped
        private LocalDateTime startedAt;
        private LocalDateTime completedAt;
        private Long durationMs;
        private Boolean isRequired;
        private Boolean isBlocking;
        private List<String> requirements;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WorkflowActionDto {
        private String actionId;
        private String actionName;
        private String actionDescription;
        private String actionType; // user_action, system_action, external_action
        private Boolean isRequired;
        private Boolean isBlocking;
        private String targetState;
        private Map<String, Object> actionParameters;
        private List<String> requiredPermissions;
        private LocalDateTime availableUntil;
    }
}

/**
 * DTO for workflow state transitions
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class WorkflowTransitionDto {

    private String profileId;
    private String fromState;
    private String toState;
    private String transitionReason;
    private Map<String, Object> transitionData;
    private String triggeredBy;
    private Boolean triggeredBySystem;
    private Long durationInPreviousStateMs;
    private LocalDateTime createdAt;
    private TransitionMetricsDto metrics;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TransitionMetricsDto {
        private Long averageDurationInState;
        private Long minDurationInState;
        private Long maxDurationInState;
        private Integer transitionCount;
        private Double successRate;
        private List<String> commonFailureReasons;
    }
}

/**
 * DTO for business rule validation results
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class BusinessRuleValidationDto {

    private String profileId;
    private String ruleName;
    private String ruleCategory;
    private String validationStatus; // PENDING, PASSED, FAILED, SKIPPED, ERROR
    private Map<String, Object> validationResult;
    private String errorMessage;
    private Boolean isBlocking;
    private Integer retryCount;
    private LocalDateTime lastValidatedAt;
    private LocalDateTime nextValidationAt;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private RuleDetailsDto ruleDetails;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RuleDetailsDto {
        private String ruleDescription;
        private String ruleType;
        private List<String> dependencies;
        private Map<String, Object> ruleParameters;
        private String validationMethod;
        private Integer maxRetries;
        private Long retryDelayMs;
    }
}

/**
 * DTO for validation results summary
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class ValidationResultDto {

    private String profileId;
    private String overallStatus; // PASSED, FAILED, PARTIAL, PENDING
    private Integer totalRules;
    private Integer passedRules;
    private Integer failedRules;
    private Integer pendingRules;
    private Integer skippedRules;
    private Double completionPercentage;
    private List<BusinessRuleValidationDto> validationResults;
    private List<BusinessRuleValidationDto> blockingIssues;
    private List<String> nextRequiredActions;
    private LocalDateTime lastValidatedAt;
    private LocalDateTime nextValidationAt;
    private ValidationMetricsDto metrics;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ValidationMetricsDto {
        private Long totalValidationTimeMs;
        private Long averageRuleValidationTimeMs;
        private Integer retryCount;
        private Double successRate;
        private List<String> mostFailedRules;
        private List<String> slowestRules;
    }
}

/**
 * DTO for authorization context
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class AuthorizationContextDto {

    private Long userId;
    private Long accountId;
    private List<String> userRoles;
    private String ipAddress;
    private String userAgent;
    private String sessionId;
    private LocalDateTime requestTime;
    private Map<String, Object> additionalContext;
    private SecurityContextDto securityContext;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SecurityContextDto {
        private Boolean isAuthenticated;
        private String authenticationMethod;
        private LocalDateTime lastLoginAt;
        private Integer failedLoginAttempts;
        private Boolean accountLocked;
        private Boolean requiresMfa;
        private Boolean mfaVerified;
        private List<String> permissions;
        private Map<String, Object> securityFlags;
    }
}
