package com.gumtree.tns.identityverification.dao.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Entity representing GBG Portal User Activity
 */
@Entity
@Table(name = "gbg_portal_user_activity")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GbgPortalUserActivity {

    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", updatable = false, nullable = false)
    private UUID id;

    @Column(name = "gbg_activity_id", nullable = false, unique = true)
    private String gbgActivityId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "gbg_portal_user_id", nullable = false)
    private GbgPortalUser gbgPortalUser;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "gbg_profile_id")
    private GbgProfile gbgProfile;

    @Enumerated(EnumType.STRING)
    @Column(name = "activity_type", nullable = false)
    private ActivityType activityType;

    @Column(name = "ip_address")
    private String ipAddress;

    @Column(name = "user_agent", columnDefinition = "TEXT")
    private String userAgent;

    @Type(type = "jsonb")
    @Column(name = "activity_details", columnDefinition = "jsonb")
    private String activityDetails;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // Additional fields for enhanced activity tracking
    @Column(name = "session_id")
    private String sessionId;

    @Column(name = "activity_name")
    private String activityName;

    @Column(name = "description")
    private String description;

    @Column(name = "is_successful")
    @Builder.Default
    private Boolean isSuccessful = true;

    @Column(name = "error_message")
    private String errorMessage;

    @Column(name = "duration_ms")
    private Long durationMs;

    @Column(name = "device_type")
    private String deviceType;

    @Column(name = "browser_name")
    private String browserName;

    @Column(name = "operating_system")
    private String operatingSystem;

    @Column(name = "location")
    private String location;

    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        if (createdAt == null) {
            createdAt = now;
        }
        if (updatedAt == null) {
            updatedAt = now;
        }
        if (gbgActivityId == null) {
            gbgActivityId = "activity_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    /**
     * Activity types for portal users
     */
    public enum ActivityType {
        ACCESS_LOG("access_log"),
        FORM_SUBMISSION("form_submission"),
        DOCUMENT_UPLOAD("document_upload"),
        PROFILE_UPDATE("profile_update"),
        LOGIN("login"),
        LOGOUT("logout"),
        PASSWORD_CHANGE("password_change"),
        EMAIL_CHANGE("email_change"),
        SETTINGS_UPDATE("settings_update"),
        DOCUMENT_DOWNLOAD("document_download"),
        DOCUMENT_DELETE("document_delete"),
        FORM_SAVE("form_save"),
        FORM_SUBMIT("form_submit"),
        PROFILE_VIEW("profile_view"),
        CHECKLIST_UPDATE("checklist_update"),
        COMMENT_ADD("comment_add"),
        NOTIFICATION_VIEW("notification_view"),
        HELP_ACCESS("help_access"),
        SUPPORT_REQUEST("support_request");

        private final String value;

        ActivityType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static ActivityType fromValue(String value) {
            for (ActivityType type : values()) {
                if (type.value.equals(value)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("Unknown activity type: " + value);
        }
    }

    /**
     * Check if activity is a login/logout event
     */
    public Boolean isAuthenticationActivity() {
        return ActivityType.LOGIN.equals(activityType) || ActivityType.LOGOUT.equals(activityType);
    }

    /**
     * Check if activity is a document-related event
     */
    public Boolean isDocumentActivity() {
        return ActivityType.DOCUMENT_UPLOAD.equals(activityType) || 
               ActivityType.DOCUMENT_DOWNLOAD.equals(activityType) || 
               ActivityType.DOCUMENT_DELETE.equals(activityType);
    }

    /**
     * Check if activity is a form-related event
     */
    public Boolean isFormActivity() {
        return ActivityType.FORM_SUBMISSION.equals(activityType) || 
               ActivityType.FORM_SAVE.equals(activityType) || 
               ActivityType.FORM_SUBMIT.equals(activityType);
    }

    /**
     * Check if activity was successful
     */
    public Boolean wasSuccessful() {
        return Boolean.TRUE.equals(isSuccessful) && errorMessage == null;
    }

    /**
     * Get activity duration in seconds
     */
    public Double getDurationInSeconds() {
        return durationMs != null ? durationMs / 1000.0 : null;
    }

    /**
     * Check if activity is recent (within last hour)
     */
    public Boolean isRecent() {
        return createdAt != null && createdAt.isAfter(LocalDateTime.now().minusHours(1));
    }
}
