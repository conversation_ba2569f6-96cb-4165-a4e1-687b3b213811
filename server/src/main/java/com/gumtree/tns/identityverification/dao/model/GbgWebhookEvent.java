package com.gumtree.tns.identityverification.dao.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Entity representing GBG Webhook Events
 */
@Entity
@Table(name = "gbg_webhook_events")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GbgWebhookEvent {

    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", updatable = false, nullable = false)
    private UUID id;

    @Column(name = "event_id", nullable = false, unique = true)
    private String eventId;

    @Enumerated(EnumType.STRING)
    @Column(name = "event_type", nullable = false)
    private WebhookEventType eventType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "gbg_profile_id")
    private GbgProfile gbgProfile;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "gbg_representative_id")
    private GbgRepresentative gbgRepresentative;

    @Column(name = "source", nullable = false)
    @Builder.Default
    private String source = "gbg";

    @Column(name = "version")
    private String version;

    @Type(type = "jsonb")
    @Column(name = "event_data", columnDefinition = "jsonb")
    private String eventData;

    @Type(type = "jsonb")
    @Column(name = "headers", columnDefinition = "jsonb")
    private String headers;

    @Column(name = "raw_payload", columnDefinition = "TEXT")
    private String rawPayload;

    @Column(name = "signature")
    private String signature;

    @Column(name = "signature_verified")
    @Builder.Default
    private Boolean signatureVerified = false;

    @Enumerated(EnumType.STRING)
    @Column(name = "processing_status", nullable = false)
    @Builder.Default
    private ProcessingStatus processingStatus = ProcessingStatus.PENDING;

    @Column(name = "processing_result", columnDefinition = "TEXT")
    private String processingResult;

    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    @Column(name = "retry_count", nullable = false)
    @Builder.Default
    private Integer retryCount = 0;

    @Column(name = "max_retries", nullable = false)
    @Builder.Default
    private Integer maxRetries = 3;

    @Column(name = "next_retry_at")
    private LocalDateTime nextRetryAt;

    @Column(name = "received_at", nullable = false)
    private LocalDateTime receivedAt;

    @Column(name = "processed_at")
    private LocalDateTime processedAt;

    @Column(name = "processing_time_ms")
    private Long processingTimeMs;

    @Column(name = "processing_node")
    private String processingNode;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        if (createdAt == null) {
            createdAt = now;
        }
        if (updatedAt == null) {
            updatedAt = now;
        }
        if (receivedAt == null) {
            receivedAt = now;
        }
        if (eventId == null) {
            eventId = "webhook_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().substring(0, 8);
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    /**
     * Webhook event types
     */
    public enum WebhookEventType {
        PROFILE_CREATED("profile.created"),
        PROFILE_UPDATED("profile.updated"),
        PROFILE_DELETED("profile.deleted"),
        REPRESENTATIVE_CREATED("representative.created"),
        REPRESENTATIVE_UPDATED("representative.updated"),
        REPRESENTATIVE_VERIFICATION_STARTED("representative.verification.started"),
        REPRESENTATIVE_VERIFICATION_COMPLETED("representative.verification.completed"),
        REPRESENTATIVE_VERIFICATION_FAILED("representative.verification.failed"),
        ANALYSIS_STARTED("analysis.started"),
        ANALYSIS_COMPLETED("analysis.completed"),
        ANALYSIS_FAILED("analysis.failed"),
        CHECKLIST_CREATED("checklist.created"),
        CHECKLIST_UPDATED("checklist.updated"),
        CHECKLIST_COMPLETED("checklist.completed"),
        PORTAL_USER_CREATED("portal_user.created"),
        PORTAL_USER_UPDATED("portal_user.updated"),
        PORTAL_USER_ACTIVITY("portal_user.activity"),
        DOCUMENT_UPLOADED("document.uploaded"),
        DOCUMENT_PROCESSED("document.processed"),
        WORKFLOW_STATE_CHANGED("workflow.state_changed");

        private final String value;

        WebhookEventType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static WebhookEventType fromValue(String value) {
            for (WebhookEventType type : values()) {
                if (type.value.equals(value)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("Unknown webhook event type: " + value);
        }
    }

    /**
     * Processing status enum
     */
    public enum ProcessingStatus {
        PENDING("pending"),
        PROCESSING("processing"),
        COMPLETED("completed"),
        FAILED("failed"),
        RETRYING("retrying"),
        SKIPPED("skipped");

        private final String value;

        ProcessingStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static ProcessingStatus fromValue(String value) {
            for (ProcessingStatus status : values()) {
                if (status.value.equals(value)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("Unknown processing status: " + value);
        }
    }

    /**
     * Check if event can be retried
     */
    public Boolean canRetry() {
        return ProcessingStatus.FAILED.equals(processingStatus) && 
               retryCount < maxRetries &&
               (nextRetryAt == null || nextRetryAt.isBefore(LocalDateTime.now()));
    }

    /**
     * Check if event is terminal (no more processing needed)
     */
    public Boolean isTerminal() {
        return ProcessingStatus.COMPLETED.equals(processingStatus) ||
               ProcessingStatus.SKIPPED.equals(processingStatus) ||
               (ProcessingStatus.FAILED.equals(processingStatus) && retryCount >= maxRetries);
    }

    /**
     * Check if event requires immediate processing
     */
    public Boolean requiresImmediateProcessing() {
        return eventType == WebhookEventType.REPRESENTATIVE_VERIFICATION_COMPLETED ||
               eventType == WebhookEventType.ANALYSIS_COMPLETED ||
               eventType == WebhookEventType.CHECKLIST_COMPLETED ||
               eventType == WebhookEventType.WORKFLOW_STATE_CHANGED;
    }

    /**
     * Calculate next retry time with exponential backoff
     */
    public LocalDateTime calculateNextRetryTime() {
        if (retryCount >= maxRetries) {
            return null;
        }
        
        // Exponential backoff: 1min, 2min, 4min, 8min, etc.
        long delayMinutes = (long) Math.pow(2, retryCount);
        return LocalDateTime.now().plusMinutes(delayMinutes);
    }

    /**
     * Mark event as processing
     */
    public void markAsProcessing(String processingNode) {
        this.processingStatus = ProcessingStatus.PROCESSING;
        this.processingNode = processingNode;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Mark event as completed
     */
    public void markAsCompleted(String result, Long processingTimeMs) {
        this.processingStatus = ProcessingStatus.COMPLETED;
        this.processingResult = result;
        this.processedAt = LocalDateTime.now();
        this.processingTimeMs = processingTimeMs;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Mark event as failed and schedule retry if possible
     */
    public void markAsFailed(String errorMessage, Long processingTimeMs) {
        this.processingStatus = ProcessingStatus.FAILED;
        this.errorMessage = errorMessage;
        this.processedAt = LocalDateTime.now();
        this.processingTimeMs = processingTimeMs;
        
        if (retryCount < maxRetries) {
            this.retryCount++;
            this.nextRetryAt = calculateNextRetryTime();
            this.processingStatus = ProcessingStatus.RETRYING;
        }
        
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Get processing duration in seconds
     */
    public Double getProcessingDurationInSeconds() {
        return processingTimeMs != null ? processingTimeMs / 1000.0 : null;
    }

    /**
     * Get time since received in minutes
     */
    public Long getMinutesSinceReceived() {
        return java.time.temporal.ChronoUnit.MINUTES.between(receivedAt, LocalDateTime.now());
    }
}
