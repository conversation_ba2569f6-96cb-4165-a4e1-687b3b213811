package com.gumtree.tns.identityverification.controller;

import com.gumtree.tns.identityverification.service.GbgSearchService;
import com.gumtree.tns.identityverification.service.dto.SearchRequestDto;
import com.gumtree.tns.identityverification.service.dto.SearchResponseDto;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * Controller for company search operations
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/company-search")
@RequiredArgsConstructor
@Validated
public class CompanySearchController {

    private final GbgSearchService gbgSearchService;
    private final MeterRegistry meterRegistry;
    private final Timer searchTimer;

    public CompanySearchController(GbgSearchService gbgSearchService, MeterRegistry meterRegistry) {
        this.gbgSearchService = gbgSearchService;
        this.meterRegistry = meterRegistry;
        this.searchTimer = Timer.builder("company.search.duration")
            .description("Duration of company search operations")
            .register(meterRegistry);
    }

    /**
     * Search for companies using GBG API
     * POST /api/v1/company-search/search
     */
    @PostMapping("/search")
    public ResponseEntity<SearchResponseDto> searchCompanies(
            @Valid @RequestBody SearchRequestDto searchRequest,
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "accountId", required = false) Long accountId) {

        log.atInfo().log("Company search request received for name: {}, country: {}", 
            searchRequest.getName(), searchRequest.getAddress().getCountryCode());

        return searchTimer.recordCallable(() -> {
            try {
                // Validate that either userId or accountId is provided
                if (userId == null && accountId == null) {
                    log.atWarn().log("Company search request missing both userId and accountId");
                    return ResponseEntity.badRequest().build();
                }

                Counter.builder("company.search.requests.total")
                    .tag("type", searchRequest.getType())
                    .tag("country", searchRequest.getAddress().getCountryCode())
                    .register(meterRegistry)
                    .increment();

                SearchResponseDto response = gbgSearchService.searchCompanies(searchRequest, userId, accountId);

                Counter.builder("company.search.requests.success.total")
                    .tag("type", searchRequest.getType())
                    .tag("results", String.valueOf(response.getTotalResults()))
                    .register(meterRegistry)
                    .increment();

                log.atInfo().log("Company search completed successfully. Results: {}, LookupId: {}", 
                    response.getTotalResults(), response.getLookupId());

                return ResponseEntity.ok(response);

            } catch (Exception e) {
                Counter.builder("company.search.requests.error.total")
                    .tag("type", searchRequest.getType())
                    .tag("error", e.getClass().getSimpleName())
                    .register(meterRegistry)
                    .increment();

                log.atError().withCause(e).log("Company search failed");
                throw e;
            }
        });
    }

    /**
     * Get search history for a user or account
     * GET /api/v1/company-search/history
     */
    @GetMapping("/history")
    public ResponseEntity<SearchResponseDto> getSearchHistory(
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "accountId", required = false) Long accountId,
            @RequestParam(value = "limit", defaultValue = "10") 
            @Min(value = 1, message = "Limit must be at least 1")
            @Max(value = 100, message = "Limit cannot exceed 100") Integer limit) {

        log.atInfo().log("Search history request for userId: {}, accountId: {}, limit: {}", 
            userId, accountId, limit);

        try {
            // Validate that either userId or accountId is provided
            if (userId == null && accountId == null) {
                log.atWarn().log("Search history request missing both userId and accountId");
                return ResponseEntity.badRequest().build();
            }

            SearchResponseDto response = gbgSearchService.getSearchHistory(userId, accountId, limit);

            log.atInfo().log("Search history retrieved successfully. Total results: {}", 
                response.getTotalResults());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to retrieve search history");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get search results by lookup ID
     * GET /api/v1/company-search/results/{lookupId}
     */
    @GetMapping("/results/{lookupId}")
    public ResponseEntity<SearchResponseDto> getSearchResults(
            @PathVariable("lookupId") @NotNull Integer lookupId) {

        log.atInfo().log("Search results request for lookupId: {}", lookupId);

        try {
            SearchResponseDto response = gbgSearchService.getSearchResults(lookupId);

            log.atInfo().log("Search results retrieved successfully for lookupId: {}. Results: {}", 
                lookupId, response.getTotalResults());

            return ResponseEntity.ok(response);

        } catch (RuntimeException e) {
            if (e.getMessage().contains("not found")) {
                log.atWarn().log("Search results not found for lookupId: {}", lookupId);
                return ResponseEntity.notFound().build();
            }
            
            log.atError().withCause(e).log("Failed to retrieve search results for lookupId: {}", lookupId);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Health check endpoint for search service
     * GET /api/v1/company-search/health
     */
    @GetMapping("/health")
    public ResponseEntity<String> healthCheck() {
        return ResponseEntity.ok("Company search service is healthy");
    }
}
