package com.gumtree.tns.identityverification.dao.repository;

import com.gumtree.tns.identityverification.dao.model.GbgProfile;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository for GBG Profile operations
 */
@Repository
public interface GbgProfileRepository extends JpaRepository<GbgProfile, UUID> {

    /**
     * Find profile by GBG UUID
     */
    Optional<GbgProfile> findByGbgProfileUuid(String gbgProfileUuid);

    /**
     * Find profile by customer reference
     */
    Optional<GbgProfile> findByCustomerReference(String customerReference);

    /**
     * Find profiles by review status
     */
    List<GbgProfile> findByReviewStatusOrderByCreatedAtDesc(GbgProfile.ReviewStatus reviewStatus);

    /**
     * Find profiles by profile type
     */
    List<GbgProfile> findByProfileTypeOrderByCreatedAtDesc(GbgProfile.ProfileType profileType);

    /**
     * Find profiles by risk category
     */
    List<GbgProfile> findByRiskCategoryOrderByCreatedAtDesc(GbgProfile.RiskCategory riskCategory);

    /**
     * Find profiles by team ID
     */
    List<GbgProfile> findByTeamIdOrderByCreatedAtDesc(Integer teamId);

    /**
     * Find profiles by assignee ID
     */
    List<GbgProfile> findByAssigneeIdOrderByCreatedAtDesc(String assigneeId);

    /**
     * Find profiles with pagination and filtering
     */
    @Query("SELECT p FROM GbgProfile p WHERE " +
           "(:reviewStatus IS NULL OR p.reviewStatus = :reviewStatus) AND " +
           "(:teamId IS NULL OR p.teamId = :teamId) AND " +
           "(:assigneeId IS NULL OR p.assigneeId = :assigneeId) AND " +
           "(:riskCategory IS NULL OR p.riskCategory = :riskCategory) AND " +
           "(:customerReference IS NULL OR p.customerReference LIKE %:customerReference%) AND " +
           "(:configurationId IS NULL OR p.configurationId = :configurationId)")
    Page<GbgProfile> findProfilesWithFilters(
        @Param("reviewStatus") GbgProfile.ReviewStatus reviewStatus,
        @Param("teamId") Integer teamId,
        @Param("assigneeId") String assigneeId,
        @Param("riskCategory") GbgProfile.RiskCategory riskCategory,
        @Param("customerReference") String customerReference,
        @Param("configurationId") Integer configurationId,
        Pageable pageable);

    /**
     * Find profiles by company name (case-insensitive)
     */
    @Query("SELECT p FROM GbgProfile p WHERE LOWER(p.companyName) LIKE LOWER(CONCAT('%', :companyName, '%'))")
    List<GbgProfile> findByCompanyNameContainingIgnoreCase(@Param("companyName") String companyName);

    /**
     * Find profiles by company number
     */
    Optional<GbgProfile> findByCompanyNumber(String companyNumber);

    /**
     * Find profiles by VAT number
     */
    Optional<GbgProfile> findByVatNumber(String vatNumber);

    /**
     * Find profiles by domain
     */
    List<GbgProfile> findByDomainOrderByCreatedAtDesc(String domain);

    /**
     * Count profiles by review status
     */
    Long countByReviewStatus(GbgProfile.ReviewStatus reviewStatus);

    /**
     * Count profiles by risk category
     */
    Long countByRiskCategory(GbgProfile.RiskCategory riskCategory);

    /**
     * Find profiles that need review (status = NEEDS_REVIEW or ADDITIONAL_INFO_REQUIRED)
     */
    @Query("SELECT p FROM GbgProfile p WHERE p.reviewStatus IN ('NEEDS_REVIEW', 'ADDITIONAL_INFO_REQUIRED') ORDER BY p.createdAt ASC")
    List<GbgProfile> findProfilesNeedingReview();

    /**
     * Find profiles assigned to a specific team member
     */
    @Query("SELECT p FROM GbgProfile p WHERE p.teamId = :teamId AND p.assigneeId = :assigneeId ORDER BY p.createdAt DESC")
    List<GbgProfile> findByTeamIdAndAssigneeId(@Param("teamId") Integer teamId, @Param("assigneeId") String assigneeId);

    /**
     * Find profiles created within a date range
     */
    @Query("SELECT p FROM GbgProfile p WHERE p.createdAt >= :startDate AND p.createdAt <= :endDate ORDER BY p.createdAt DESC")
    List<GbgProfile> findByCreatedAtBetween(@Param("startDate") java.time.LocalDateTime startDate, 
                                           @Param("endDate") java.time.LocalDateTime endDate);

    /**
     * Check if customer reference exists
     */
    boolean existsByCustomerReference(String customerReference);

    /**
     * Check if GBG profile UUID exists
     */
    boolean existsByGbgProfileUuid(String gbgProfileUuid);
}
