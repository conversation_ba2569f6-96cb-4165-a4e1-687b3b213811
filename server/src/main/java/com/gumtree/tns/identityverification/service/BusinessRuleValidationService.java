package com.gumtree.tns.identityverification.service;

import com.gumtree.tns.identityverification.service.dto.BusinessRuleValidationDto;
import com.gumtree.tns.identityverification.service.dto.ValidationResultDto;

import java.util.List;

/**
 * Service for validating business rules during verification process
 */
public interface BusinessRuleValidationService {

    /**
     * Validate all business rules for a profile
     * 
     * @param profileUuid the GBG profile UUID
     * @return validation results
     */
    ValidationResultDto validateAllRules(String profileUuid);

    /**
     * Validate specific business rule
     * 
     * @param profileUuid the GBG profile UUID
     * @param ruleName the rule name to validate
     * @return validation result
     */
    BusinessRuleValidationDto validateRule(String profileUuid, String ruleName);

    /**
     * Validate rules for specific category
     * 
     * @param profileUuid the GBG profile UUID
     * @param category the rule category
     * @return validation results
     */
    List<BusinessRuleValidationDto> validateRulesByCategory(String profileUuid, RuleCategory category);

    /**
     * Get validation status for profile
     * 
     * @param profileUuid the GBG profile UUID
     * @return current validation status
     */
    ValidationResultDto getValidationStatus(String profileUuid);

    /**
     * Check if profile can proceed to next workflow state
     * 
     * @param profileUuid the GBG profile UUID
     * @param targetState the target workflow state
     * @return true if all required rules pass
     */
    boolean canProceedToState(String profileUuid, String targetState);

    /**
     * Get blocking validation issues
     * 
     * @param profileUuid the GBG profile UUID
     * @return list of blocking issues
     */
    List<BusinessRuleValidationDto> getBlockingIssues(String profileUuid);

    /**
     * Retry failed validations
     * 
     * @param profileUuid the GBG profile UUID
     * @return updated validation results
     */
    ValidationResultDto retryFailedValidations(String profileUuid);

    /**
     * Business rule categories
     */
    enum RuleCategory {
        DATA_COMPLETENESS("data_completeness", "Data Completeness Rules"),
        DATA_QUALITY("data_quality", "Data Quality Rules"),
        BUSINESS_LOGIC("business_logic", "Business Logic Rules"),
        COMPLIANCE("compliance", "Compliance Rules"),
        SECURITY("security", "Security Rules"),
        INTEGRATION("integration", "Integration Rules");

        private final String value;
        private final String description;

        RuleCategory(String value, String description) {
            this.value = value;
            this.description = description;
        }

        public String getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }

        public static RuleCategory fromValue(String value) {
            for (RuleCategory category : values()) {
                if (category.value.equals(value)) {
                    return category;
                }
            }
            throw new IllegalArgumentException("Unknown rule category: " + value);
        }
    }

    /**
     * Predefined business rules
     */
    enum BusinessRule {
        // Data Completeness Rules
        PROFILE_BASIC_INFO_COMPLETE("profile_basic_info_complete", RuleCategory.DATA_COMPLETENESS, true),
        COMPANY_INFO_COMPLETE("company_info_complete", RuleCategory.DATA_COMPLETENESS, true),
        REPRESENTATIVES_ADDED("representatives_added", RuleCategory.DATA_COMPLETENESS, true),
        REQUIRED_DOCUMENTS_UPLOADED("required_documents_uploaded", RuleCategory.DATA_COMPLETENESS, false),

        // Data Quality Rules
        COMPANY_NAME_VALID("company_name_valid", RuleCategory.DATA_QUALITY, true),
        COMPANY_NUMBER_VALID("company_number_valid", RuleCategory.DATA_QUALITY, true),
        VAT_NUMBER_VALID("vat_number_valid", RuleCategory.DATA_QUALITY, false),
        ADDRESS_VALID("address_valid", RuleCategory.DATA_QUALITY, true),
        REPRESENTATIVE_INFO_VALID("representative_info_valid", RuleCategory.DATA_QUALITY, true),

        // Business Logic Rules
        SINGLE_ACTIVE_VERIFICATION("single_active_verification", RuleCategory.BUSINESS_LOGIC, true),
        VERIFICATION_TYPE_MATCHES_ENTITY("verification_type_matches_entity", RuleCategory.BUSINESS_LOGIC, true),
        REPRESENTATIVE_COUNT_ADEQUATE("representative_count_adequate", RuleCategory.BUSINESS_LOGIC, false),
        COMPANY_STATUS_ACTIVE("company_status_active", RuleCategory.BUSINESS_LOGIC, true),

        // Compliance Rules
        SANCTIONS_CHECK_PASSED("sanctions_check_passed", RuleCategory.COMPLIANCE, true),
        PEP_CHECK_COMPLETED("pep_check_completed", RuleCategory.COMPLIANCE, true),
        AML_REQUIREMENTS_MET("aml_requirements_met", RuleCategory.COMPLIANCE, true),
        GDPR_CONSENT_OBTAINED("gdpr_consent_obtained", RuleCategory.COMPLIANCE, true),

        // Security Rules
        IDENTITY_VERIFICATION_COMPLETED("identity_verification_completed", RuleCategory.SECURITY, true),
        DOCUMENT_AUTHENTICITY_VERIFIED("document_authenticity_verified", RuleCategory.SECURITY, false),
        BIOMETRIC_VERIFICATION_PASSED("biometric_verification_passed", RuleCategory.SECURITY, false),
        FRAUD_CHECK_PASSED("fraud_check_passed", RuleCategory.SECURITY, true),

        // Integration Rules
        GBG_PROFILE_SYNCED("gbg_profile_synced", RuleCategory.INTEGRATION, true),
        EXTERNAL_DATA_VALIDATED("external_data_validated", RuleCategory.INTEGRATION, false),
        WEBHOOK_EVENTS_PROCESSED("webhook_events_processed", RuleCategory.INTEGRATION, false);

        private final String ruleName;
        private final RuleCategory category;
        private final boolean isBlocking;

        BusinessRule(String ruleName, RuleCategory category, boolean isBlocking) {
            this.ruleName = ruleName;
            this.category = category;
            this.isBlocking = isBlocking;
        }

        public String getRuleName() {
            return ruleName;
        }

        public RuleCategory getCategory() {
            return category;
        }

        public boolean isBlocking() {
            return isBlocking;
        }

        public static BusinessRule fromRuleName(String ruleName) {
            for (BusinessRule rule : values()) {
                if (rule.ruleName.equals(ruleName)) {
                    return rule;
                }
            }
            throw new IllegalArgumentException("Unknown business rule: " + ruleName);
        }

        /**
         * Get rules required for specific workflow state
         */
        public static List<BusinessRule> getRequiredRulesForState(String workflowState) {
            switch (workflowState) {
                case "PROFILE_CREATED":
                    return List.of(PROFILE_BASIC_INFO_COMPLETE, COMPANY_INFO_COMPLETE, 
                                 VERIFICATION_TYPE_MATCHES_ENTITY, SINGLE_ACTIVE_VERIFICATION);
                case "REPRESENTATIVES_ADDED":
                    return List.of(REPRESENTATIVES_ADDED, REPRESENTATIVE_INFO_VALID, 
                                 REPRESENTATIVE_COUNT_ADEQUATE);
                case "IDENTITY_VERIFICATION_COMPLETED":
                    return List.of(IDENTITY_VERIFICATION_COMPLETED, FRAUD_CHECK_PASSED);
                case "RISK_ANALYSIS_COMPLETED":
                    return List.of(SANCTIONS_CHECK_PASSED, PEP_CHECK_COMPLETED);
                case "COMPLIANCE_CHECK_COMPLETED":
                    return List.of(AML_REQUIREMENTS_MET, GDPR_CONSENT_OBTAINED);
                case "APPROVED":
                    return List.of(values()); // All rules must pass for approval
                default:
                    return List.of();
            }
        }
    }
}
