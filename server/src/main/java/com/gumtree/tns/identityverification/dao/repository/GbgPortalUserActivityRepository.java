package com.gumtree.tns.identityverification.dao.repository;

import com.gumtree.tns.identityverification.dao.model.GbgPortalUser;
import com.gumtree.tns.identityverification.dao.model.GbgPortalUserActivity;
import com.gumtree.tns.identityverification.dao.model.GbgProfile;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository for GBG Portal User Activity operations
 */
@Repository
public interface GbgPortalUserActivityRepository extends JpaRepository<GbgPortalUserActivity, UUID> {

    /**
     * Find activity by GBG activity ID
     */
    Optional<GbgPortalUserActivity> findByGbgActivityId(String gbgActivityId);

    /**
     * Find activities by portal user
     */
    List<GbgPortalUserActivity> findByGbgPortalUserOrderByCreatedAtDesc(GbgPortalUser portalUser);

    /**
     * Find activities by profile
     */
    List<GbgPortalUserActivity> findByGbgProfileOrderByCreatedAtDesc(GbgProfile profile);

    /**
     * Find activities by activity type
     */
    List<GbgPortalUserActivity> findByActivityTypeOrderByCreatedAtDesc(GbgPortalUserActivity.ActivityType activityType);

    /**
     * Find activities with pagination and filtering
     */
    @Query("SELECT a FROM GbgPortalUserActivity a WHERE " +
           "(:userId IS NULL OR a.gbgPortalUser.gbgUserId = :userId) AND " +
           "(:profileId IS NULL OR a.gbgProfile.gbgProfileUuid = :profileId) AND " +
           "(:activityType IS NULL OR a.activityType = :activityType) AND " +
           "(:ipAddress IS NULL OR a.ipAddress = :ipAddress) AND " +
           "(:isSuccessful IS NULL OR a.isSuccessful = :isSuccessful)")
    Page<GbgPortalUserActivity> findActivitiesWithFilters(
        @Param("userId") Integer userId,
        @Param("profileId") String profileId,
        @Param("activityType") GbgPortalUserActivity.ActivityType activityType,
        @Param("ipAddress") String ipAddress,
        @Param("isSuccessful") Boolean isSuccessful,
        Pageable pageable);

    /**
     * Find activities by session ID
     */
    List<GbgPortalUserActivity> findBySessionIdOrderByCreatedAtDesc(String sessionId);

    /**
     * Find activities by IP address
     */
    List<GbgPortalUserActivity> findByIpAddressOrderByCreatedAtDesc(String ipAddress);

    /**
     * Find activities created within date range
     */
    @Query("SELECT a FROM GbgPortalUserActivity a WHERE a.createdAt >= :startDate AND a.createdAt <= :endDate ORDER BY a.createdAt DESC")
    List<GbgPortalUserActivity> findByCreatedAtBetweenOrderByCreatedAtDesc(
        @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * Find recent activities for a user
     */
    @Query("SELECT a FROM GbgPortalUserActivity a WHERE a.gbgPortalUser = :user AND a.createdAt >= :since ORDER BY a.createdAt DESC")
    List<GbgPortalUserActivity> findRecentActivitiesByUser(@Param("user") GbgPortalUser user, @Param("since") LocalDateTime since);

    /**
     * Find recent activities for a profile
     */
    @Query("SELECT a FROM GbgPortalUserActivity a WHERE a.gbgProfile = :profile AND a.createdAt >= :since ORDER BY a.createdAt DESC")
    List<GbgPortalUserActivity> findRecentActivitiesByProfile(@Param("profile") GbgProfile profile, @Param("since") LocalDateTime since);

    /**
     * Find failed activities
     */
    @Query("SELECT a FROM GbgPortalUserActivity a WHERE a.isSuccessful = false ORDER BY a.createdAt DESC")
    List<GbgPortalUserActivity> findFailedActivities();

    /**
     * Find failed activities for a user
     */
    @Query("SELECT a FROM GbgPortalUserActivity a WHERE a.gbgPortalUser = :user AND a.isSuccessful = false ORDER BY a.createdAt DESC")
    List<GbgPortalUserActivity> findFailedActivitiesByUser(@Param("user") GbgPortalUser user);

    /**
     * Find login activities
     */
    @Query("SELECT a FROM GbgPortalUserActivity a WHERE a.activityType = 'LOGIN' ORDER BY a.createdAt DESC")
    List<GbgPortalUserActivity> findLoginActivities();

    /**
     * Find login activities for a user
     */
    @Query("SELECT a FROM GbgPortalUserActivity a WHERE a.gbgPortalUser = :user AND a.activityType = 'LOGIN' ORDER BY a.createdAt DESC")
    List<GbgPortalUserActivity> findLoginActivitiesByUser(@Param("user") GbgPortalUser user);

    /**
     * Find document upload activities
     */
    @Query("SELECT a FROM GbgPortalUserActivity a WHERE a.activityType = 'DOCUMENT_UPLOAD' ORDER BY a.createdAt DESC")
    List<GbgPortalUserActivity> findDocumentUploadActivities();

    /**
     * Find form submission activities
     */
    @Query("SELECT a FROM GbgPortalUserActivity a WHERE a.activityType = 'FORM_SUBMISSION' ORDER BY a.createdAt DESC")
    List<GbgPortalUserActivity> findFormSubmissionActivities();

    /**
     * Count activities by user
     */
    Long countByGbgPortalUser(GbgPortalUser portalUser);

    /**
     * Count activities by profile
     */
    Long countByGbgProfile(GbgProfile profile);

    /**
     * Count activities by type
     */
    Long countByActivityType(GbgPortalUserActivity.ActivityType activityType);

    /**
     * Count successful activities
     */
    @Query("SELECT COUNT(a) FROM GbgPortalUserActivity a WHERE a.isSuccessful = true")
    Long countSuccessfulActivities();

    /**
     * Count failed activities
     */
    @Query("SELECT COUNT(a) FROM GbgPortalUserActivity a WHERE a.isSuccessful = false")
    Long countFailedActivities();

    /**
     * Count activities within date range
     */
    @Query("SELECT COUNT(a) FROM GbgPortalUserActivity a WHERE a.createdAt >= :startDate AND a.createdAt <= :endDate")
    Long countActivitiesBetween(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * Get activity statistics by type
     */
    @Query("SELECT a.activityType, COUNT(a), MAX(a.createdAt) FROM GbgPortalUserActivity a GROUP BY a.activityType")
    List<Object[]> getActivityStatsByType();

    /**
     * Get activity statistics by user
     */
    @Query("SELECT a.gbgPortalUser, COUNT(a), MAX(a.createdAt) FROM GbgPortalUserActivity a GROUP BY a.gbgPortalUser ORDER BY COUNT(a) DESC")
    List<Object[]> getActivityStatsByUser();

    /**
     * Get most active users (by activity count)
     */
    @Query("SELECT a.gbgPortalUser FROM GbgPortalUserActivity a GROUP BY a.gbgPortalUser ORDER BY COUNT(a) DESC")
    List<GbgPortalUser> getMostActiveUsers(Pageable pageable);

    /**
     * Get average session duration by user
     */
    @Query("SELECT a.gbgPortalUser, AVG(a.durationMs) FROM GbgPortalUserActivity a WHERE a.durationMs IS NOT NULL GROUP BY a.gbgPortalUser")
    List<Object[]> getAverageSessionDurationByUser();

    /**
     * Find activities by device type
     */
    List<GbgPortalUserActivity> findByDeviceTypeOrderByCreatedAtDesc(String deviceType);

    /**
     * Find activities by browser
     */
    List<GbgPortalUserActivity> findByBrowserNameOrderByCreatedAtDesc(String browserName);

    /**
     * Find activities by operating system
     */
    List<GbgPortalUserActivity> findByOperatingSystemOrderByCreatedAtDesc(String operatingSystem);

    /**
     * Find activities by location
     */
    List<GbgPortalUserActivity> findByLocationOrderByCreatedAtDesc(String location);

    /**
     * Find suspicious activities (multiple IPs for same user)
     */
    @Query("SELECT a FROM GbgPortalUserActivity a WHERE a.gbgPortalUser IN " +
           "(SELECT a2.gbgPortalUser FROM GbgPortalUserActivity a2 GROUP BY a2.gbgPortalUser HAVING COUNT(DISTINCT a2.ipAddress) > 3) " +
           "ORDER BY a.createdAt DESC")
    List<GbgPortalUserActivity> findSuspiciousActivities();

    /**
     * Find activities with long duration (potential issues)
     */
    @Query("SELECT a FROM GbgPortalUserActivity a WHERE a.durationMs > :maxDurationMs ORDER BY a.durationMs DESC")
    List<GbgPortalUserActivity> findLongDurationActivities(@Param("maxDurationMs") Long maxDurationMs);

    /**
     * Check if user has recent activity
     */
    @Query("SELECT CASE WHEN COUNT(a) > 0 THEN true ELSE false END FROM GbgPortalUserActivity a WHERE " +
           "a.gbgPortalUser = :user AND a.createdAt >= :since")
    boolean hasRecentActivity(@Param("user") GbgPortalUser user, @Param("since") LocalDateTime since);

    /**
     * Get last activity for user
     */
    @Query("SELECT a FROM GbgPortalUserActivity a WHERE a.gbgPortalUser = :user ORDER BY a.createdAt DESC")
    Optional<GbgPortalUserActivity> findLastActivityByUser(@Param("user") GbgPortalUser user);

    /**
     * Delete old activities (cleanup)
     */
    @Query("DELETE FROM GbgPortalUserActivity a WHERE a.createdAt < :cutoffDate")
    void deleteOldActivities(@Param("cutoffDate") LocalDateTime cutoffDate);
}
