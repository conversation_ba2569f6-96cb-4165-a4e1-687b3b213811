package com.gumtree.tns.identityverification.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DTO for portal user responses
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PortalUserResponseDto {

    private Integer id;
    private String email;
    private String name;
    private String profileId;
    private String role;
    private String status; // active, inactive, pending, suspended
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private LocalDateTime lastLoginAt;
    private String language;
    private String timezone;
    private PortalUserPreferencesDto preferences;
    private List<PortalUserActivitySummaryDto> recentActivity;
    private PortalUserStatsDto statistics;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PortalUserPreferencesDto {
        private Boolean emailNotifications;
        private Boolean smsNotifications;
        private String notificationFrequency;
        private String documentUploadNotifications;
        private String complianceUpdateNotifications;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PortalUserActivitySummaryDto {
        private String activityType;
        private String description;
        private LocalDateTime timestamp;
        private String ipAddress;
        private String userAgent;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PortalUserStatsDto {
        private Integer totalUsers;
        private Integer activeUsers;
        private Integer pendingUsers;
        private Integer suspendedUsers;
        private Integer totalLogins;
        private Integer documentsUploaded;
        private Integer formsCompleted;
        private LocalDateTime lastActivityDate;
        private Double averageSessionDuration; // in minutes
        private List<String> mostActiveUsers;
        private List<ActivityTypeStatsDto> activityBreakdown;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ActivityTypeStatsDto {
        private String activityType;
        private Integer count;
        private LocalDateTime lastOccurrence;
    }
}
