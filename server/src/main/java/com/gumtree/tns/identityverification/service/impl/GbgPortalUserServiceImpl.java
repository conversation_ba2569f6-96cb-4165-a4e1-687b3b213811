package com.gumtree.tns.identityverification.service.impl;

import com.gumtree.tns.identityverification.dao.model.GbgPortalUser;
import com.gumtree.tns.identityverification.dao.model.GbgPortalUserActivity;
import com.gumtree.tns.identityverification.dao.model.GbgProfile;
import com.gumtree.tns.identityverification.dao.repository.GbgPortalUserActivityRepository;
import com.gumtree.tns.identityverification.dao.repository.GbgPortalUserRepository;
import com.gumtree.tns.identityverification.dao.repository.GbgProfileRepository;
import com.gumtree.tns.identityverification.gateway.client.GbgApiClient;
import com.gumtree.tns.identityverification.gateway.client.dto.GbgApiClientDtos.*;
import com.gumtree.tns.identityverification.service.GbgPortalUserService;
import com.gumtree.tns.identityverification.service.dto.PortalUserActivityDto;
import com.gumtree.tns.identityverification.service.dto.PortalUserRequestDto;
import com.gumtree.tns.identityverification.service.dto.PortalUserResponseDto;
import com.gumtree.tns.identityverification.service.mapper.GbgPortalUserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Implementation of GBG Portal User service
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GbgPortalUserServiceImpl implements GbgPortalUserService {

    private final GbgApiClient gbgApiClient;
    private final GbgPortalUserRepository portalUserRepository;
    private final GbgPortalUserActivityRepository activityRepository;
    private final GbgProfileRepository profileRepository;
    private final GbgPortalUserMapper portalUserMapper;

    @Override
    @Transactional
    public PortalUserResponseDto createPortalUser(PortalUserRequestDto request) {
        log.atInfo().log("Creating portal user with email: {}", request.getEmail());

        try {
            // Check if email already exists
            if (portalUserRepository.existsByEmail(request.getEmail())) {
                throw new RuntimeException("Email already exists: " + request.getEmail());
            }

            // Get profile if specified
            GbgProfile profile = null;
            if (request.getProfileId() != null) {
                profile = profileRepository.findByGbgProfileUuid(request.getProfileId())
                    .orElseThrow(() -> new RuntimeException("Profile not found: " + request.getProfileId()));
            }

            // Create portal user via GBG API
            GbgCreatePortalUserRequest gbgRequest = portalUserMapper.toGbgCreateRequest(request);
            GbgApiResponse<GbgPortalUserResponse> gbgResponse = gbgApiClient.createPortalUser(gbgRequest);

            if (gbgResponse.getData() == null) {
                throw new RuntimeException("Failed to create portal user in GBG");
            }

            // Save portal user locally
            GbgPortalUser portalUser = portalUserMapper.toEntity(gbgResponse.getData(), profile);
            portalUser = portalUserRepository.save(portalUser);

            // Send invitation if requested
            if (Boolean.TRUE.equals(request.getSendInvitation())) {
                sendInvitationEmail(portalUser.getId().toString(), request.getProfileId());
            }

            // Log activity
            logPortalUserActivity(portalUser, GbgPortalUserActivity.ActivityType.ACCESS_LOG, 
                "Portal user created", null, null);

            log.atInfo().log("Portal user created successfully with ID: {}", portalUser.getId());
            return portalUserMapper.toResponseDto(portalUser);

        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to create portal user with email: {}", request.getEmail());
            throw new RuntimeException("Failed to create portal user", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public PortalUserResponseDto getPortalUser(String userId) {
        log.atInfo().log("Getting portal user: {}", userId);

        try {
            UUID userUuid = UUID.fromString(userId);
            GbgPortalUser portalUser = portalUserRepository.findById(userUuid)
                .orElseThrow(() -> new RuntimeException("Portal user not found: " + userId));

            // Update last accessed
            portalUser.setLastLoginAt(LocalDateTime.now());
            portalUserRepository.save(portalUser);

            return portalUserMapper.toResponseDto(portalUser);

        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to get portal user: {}", userId);
            throw new RuntimeException("Failed to retrieve portal user", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<PortalUserResponseDto> getPortalUsers(String email, String profileId, Integer page, Integer size) {
        log.atInfo().log("Getting portal users with filters - email: {}, profileId: {}, page: {}, size: {}", 
            email, profileId, page, size);

        try {
            Pageable pageable = PageRequest.of(page != null ? page : 0, size != null ? size : 20);
            
            Page<GbgPortalUser> portalUsers = portalUserRepository.findPortalUsersWithFilters(
                email, profileId, null, null, pageable);

            return portalUsers.getContent().stream()
                .map(portalUserMapper::toResponseDto)
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to get portal users");
            throw new RuntimeException("Failed to retrieve portal users", e);
        }
    }

    @Override
    @Transactional
    public PortalUserResponseDto assignPortalUserToProfile(String profileUuid, Integer userId, String role) {
        log.atInfo().log("Assigning portal user {} to profile {} with role {}", userId, profileUuid, role);

        try {
            // Get portal user
            GbgPortalUser portalUser = portalUserRepository.findByGbgUserId(userId)
                .orElseThrow(() -> new RuntimeException("Portal user not found: " + userId));

            // Get profile
            GbgProfile profile = profileRepository.findByGbgProfileUuid(profileUuid)
                .orElseThrow(() -> new RuntimeException("Profile not found: " + profileUuid));

            // Update assignment via GBG API
            GbgAssignPortalUserRequest gbgRequest = GbgAssignPortalUserRequest.builder()
                .profileUuid(profileUuid)
                .userId(userId)
                .role(role != null ? role : "admin")
                .build();

            GbgApiResponse<GbgPortalUserResponse> gbgResponse = gbgApiClient.assignPortalUserToProfile(gbgRequest);

            if (gbgResponse.getData() == null) {
                throw new RuntimeException("Failed to assign portal user in GBG");
            }

            // Update local record
            portalUser.setGbgProfile(profile);
            portalUser.setRole(GbgPortalUser.PortalUserRole.fromValue(role != null ? role : "admin"));
            portalUser = portalUserRepository.save(portalUser);

            // Log activity
            logPortalUserActivity(portalUser, GbgPortalUserActivity.ActivityType.PROFILE_UPDATE, 
                "Assigned to profile", profileUuid, null);

            log.atInfo().log("Portal user assigned successfully");
            return portalUserMapper.toResponseDto(portalUser);

        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to assign portal user {} to profile {}", userId, profileUuid);
            throw new RuntimeException("Failed to assign portal user to profile", e);
        }
    }

    @Override
    @Transactional
    public PortalUserResponseDto updatePortalUser(String userId, PortalUserRequestDto request) {
        log.atInfo().log("Updating portal user: {}", userId);

        try {
            UUID userUuid = UUID.fromString(userId);
            GbgPortalUser portalUser = portalUserRepository.findById(userUuid)
                .orElseThrow(() -> new RuntimeException("Portal user not found: " + userId));

            // Update via GBG API
            GbgUpdatePortalUserRequest gbgRequest = portalUserMapper.toGbgUpdateRequest(request, portalUser.getGbgUserId());
            GbgApiResponse<GbgPortalUserResponse> gbgResponse = gbgApiClient.updatePortalUser(gbgRequest);

            if (gbgResponse.getData() == null) {
                throw new RuntimeException("Failed to update portal user in GBG");
            }

            // Update local record
            portalUserMapper.updateEntity(portalUser, request);
            portalUser = portalUserRepository.save(portalUser);

            // Log activity
            logPortalUserActivity(portalUser, GbgPortalUserActivity.ActivityType.SETTINGS_UPDATE, 
                "Portal user updated", null, null);

            log.atInfo().log("Portal user updated successfully");
            return portalUserMapper.toResponseDto(portalUser);

        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to update portal user: {}", userId);
            throw new RuntimeException("Failed to update portal user", e);
        }
    }

    @Override
    @Transactional
    public void deletePortalUser(String userId) {
        log.atInfo().log("Deleting portal user: {}", userId);

        try {
            UUID userUuid = UUID.fromString(userId);
            GbgPortalUser portalUser = portalUserRepository.findById(userUuid)
                .orElseThrow(() -> new RuntimeException("Portal user not found: " + userId));

            // Delete via GBG API
            GbgApiResponse<Void> gbgResponse = gbgApiClient.deletePortalUser(portalUser.getGbgUserId());

            // Mark as deleted locally (soft delete)
            portalUser.setStatus(GbgPortalUser.PortalUserStatus.DELETED);
            portalUserRepository.save(portalUser);

            log.atInfo().log("Portal user deleted successfully");

        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to delete portal user: {}", userId);
            throw new RuntimeException("Failed to delete portal user", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<PortalUserActivityDto> getPortalUserActivity(String userId, String profileId, String activityType, 
                                                            Integer page, Integer size) {
        log.atInfo().log("Getting portal user activity - userId: {}, profileId: {}, type: {}", 
            userId, profileId, activityType);

        try {
            Pageable pageable = PageRequest.of(page != null ? page : 0, size != null ? size : 50);
            
            Integer gbgUserId = userId != null ? Integer.valueOf(userId) : null;
            GbgPortalUserActivity.ActivityType actType = activityType != null ? 
                GbgPortalUserActivity.ActivityType.fromValue(activityType) : null;

            Page<GbgPortalUserActivity> activities = activityRepository.findActivitiesWithFilters(
                gbgUserId, profileId, actType, null, null, pageable);

            return activities.getContent().stream()
                .map(portalUserMapper::toActivityDto)
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to get portal user activity");
            throw new RuntimeException("Failed to retrieve portal user activity", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<PortalUserResponseDto> getPortalUsersByProfile(String profileUuid) {
        log.atInfo().log("Getting portal users for profile: {}", profileUuid);

        try {
            List<GbgPortalUser> portalUsers = portalUserRepository.findByProfileUuidOrderByCreatedAtDesc(profileUuid);
            
            return portalUsers.stream()
                .map(portalUserMapper::toResponseDto)
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to get portal users for profile: {}", profileUuid);
            throw new RuntimeException("Failed to retrieve portal users for profile", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isEmailAlreadyUsed(String email) {
        return portalUserRepository.existsByEmail(email);
    }

    @Override
    @Transactional
    public boolean sendInvitationEmail(String userId, String profileUuid) {
        log.atInfo().log("Sending invitation email to portal user: {}", userId);

        try {
            UUID userUuid = UUID.fromString(userId);
            GbgPortalUser portalUser = portalUserRepository.findById(userUuid)
                .orElseThrow(() -> new RuntimeException("Portal user not found: " + userId));

            // Send invitation via GBG API
            GbgSendInvitationRequest gbgRequest = GbgSendInvitationRequest.builder()
                .userId(portalUser.getGbgUserId())
                .profileUuid(profileUuid)
                .build();

            GbgApiResponse<Void> gbgResponse = gbgApiClient.sendPortalUserInvitation(gbgRequest);

            // Update invitation sent timestamp
            portalUser.setInvitationSentAt(LocalDateTime.now());
            portalUserRepository.save(portalUser);

            // Log activity
            logPortalUserActivity(portalUser, GbgPortalUserActivity.ActivityType.ACCESS_LOG, 
                "Invitation sent", profileUuid, null);

            log.atInfo().log("Invitation email sent successfully");
            return true;

        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to send invitation email to portal user: {}", userId);
            return false;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public PortalUserResponseDto.PortalUserStatsDto getPortalUserStatistics(String profileUuid) {
        log.atInfo().log("Getting portal user statistics for profile: {}", profileUuid);

        try {
            // Get basic counts
            Long totalUsers = profileUuid != null ? 
                portalUserRepository.countByGbgProfile(profileRepository.findByGbgProfileUuid(profileUuid).orElse(null)) :
                portalUserRepository.count();
            
            Long activeUsers = portalUserRepository.countByStatus(GbgPortalUser.PortalUserStatus.ACTIVE);
            Long pendingUsers = portalUserRepository.countByStatus(GbgPortalUser.PortalUserStatus.PENDING);
            Long suspendedUsers = portalUserRepository.countByStatus(GbgPortalUser.PortalUserStatus.SUSPENDED);

            // Get activity stats
            LocalDateTime lastWeek = LocalDateTime.now().minusWeeks(1);
            Long totalLogins = activityRepository.countByActivityType(GbgPortalUserActivity.ActivityType.LOGIN);
            Long documentsUploaded = activityRepository.countByActivityType(GbgPortalUserActivity.ActivityType.DOCUMENT_UPLOAD);
            Long formsCompleted = activityRepository.countByActivityType(GbgPortalUserActivity.ActivityType.FORM_SUBMISSION);

            return PortalUserResponseDto.PortalUserStatsDto.builder()
                .totalUsers(totalUsers.intValue())
                .activeUsers(activeUsers.intValue())
                .pendingUsers(pendingUsers.intValue())
                .suspendedUsers(suspendedUsers.intValue())
                .totalLogins(totalLogins.intValue())
                .documentsUploaded(documentsUploaded.intValue())
                .formsCompleted(formsCompleted.intValue())
                .lastActivityDate(LocalDateTime.now())
                .build();

        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to get portal user statistics");
            throw new RuntimeException("Failed to retrieve portal user statistics", e);
        }
    }

    private void logPortalUserActivity(GbgPortalUser portalUser, GbgPortalUserActivity.ActivityType activityType, 
                                     String description, String profileId, String ipAddress) {
        try {
            GbgPortalUserActivity activity = GbgPortalUserActivity.builder()
                .gbgPortalUser(portalUser)
                .gbgProfile(portalUser.getGbgProfile())
                .activityType(activityType)
                .activityName(activityType.getValue())
                .description(description)
                .ipAddress(ipAddress)
                .isSuccessful(true)
                .createdAt(LocalDateTime.now())
                .build();

            activityRepository.save(activity);
        } catch (Exception e) {
            log.atWarn().withCause(e).log("Failed to log portal user activity");
        }
    }
}
