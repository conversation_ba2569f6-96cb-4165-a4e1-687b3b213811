package com.gumtree.tns.identityverification.controller;

import com.gumtree.tns.identityverification.service.GbgPortalUserService;
import com.gumtree.tns.identityverification.service.VerificationAuthorizationService;
import com.gumtree.tns.identityverification.service.dto.AuthorizationContextDto;
import com.gumtree.tns.identityverification.service.dto.PortalUserActivityDto;
import com.gumtree.tns.identityverification.service.dto.PortalUserRequestDto;
import com.gumtree.tns.identityverification.service.dto.PortalUserResponseDto;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * Controller for portal user management operations
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/portal-users")
@RequiredArgsConstructor
@Validated
public class PortalUserController {

    private final GbgPortalUserService portalUserService;
    private final VerificationAuthorizationService authorizationService;
    private final MeterRegistry meterRegistry;

    /**
     * Create a new portal user
     * POST /api/v1/portal-users
     */
    @PostMapping
    public ResponseEntity<PortalUserResponseDto> createPortalUser(
            @Valid @RequestBody PortalUserRequestDto request,
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "accountId", required = false) Long accountId,
            HttpServletRequest httpRequest) {

        log.atInfo().log("Portal user creation request for email: {}", request.getEmail());

        try {
            // Build authorization context
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            List<String> userRoles = auth.getAuthorities().stream()
                .map(authority -> authority.getAuthority())
                .collect(java.util.stream.Collectors.toList());
            
            AuthorizationContextDto authContext = authorizationService.buildAuthorizationContext(
                userId, accountId, userRoles, 
                httpRequest.getRemoteAddr(), httpRequest.getHeader("User-Agent"));

            // Check authorization - only admins can create portal users
            if (!authorizationService.canPerformAdminOperations(userId, authContext)) {
                log.atWarn().log("Unauthorized portal user creation attempt from userId: {}", userId);
                authorizationService.logAuthorizationDecision("create_portal_user", userId, accountId, 
                    null, false, "Insufficient admin permissions", authContext);
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            // Check if email is already used
            if (portalUserService.isEmailAlreadyUsed(request.getEmail())) {
                log.atWarn().log("Portal user creation failed - email already exists: {}", request.getEmail());
                return ResponseEntity.status(HttpStatus.CONFLICT).build();
            }

            Counter.builder("portal.users.create.total")
                .tag("email_domain", extractEmailDomain(request.getEmail()))
                .register(meterRegistry)
                .increment();

            PortalUserResponseDto response = portalUserService.createPortalUser(request);

            Counter.builder("portal.users.create.success.total")
                .tag("email_domain", extractEmailDomain(request.getEmail()))
                .register(meterRegistry)
                .increment();

            log.atInfo().log("Portal user created successfully with ID: {}", response.getId());
            return ResponseEntity.status(HttpStatus.CREATED).body(response);

        } catch (Exception e) {
            Counter.builder("portal.users.create.error.total")
                .tag("error", e.getClass().getSimpleName())
                .register(meterRegistry)
                .increment();

            log.atError().withCause(e).log("Failed to create portal user");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get portal user by ID
     * GET /api/v1/portal-users/{userId}
     */
    @GetMapping("/{userId}")
    public ResponseEntity<PortalUserResponseDto> getPortalUser(
            @PathVariable("userId") @NotBlank String userId,
            @RequestParam(value = "requestingUserId", required = false) Long requestingUserId,
            @RequestParam(value = "accountId", required = false) Long accountId,
            HttpServletRequest httpRequest) {

        log.atInfo().log("Portal user retrieval request for ID: {}", userId);

        try {
            // Build authorization context
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            List<String> userRoles = auth.getAuthorities().stream()
                .map(authority -> authority.getAuthority())
                .collect(java.util.stream.Collectors.toList());
            
            AuthorizationContextDto authContext = authorizationService.buildAuthorizationContext(
                requestingUserId, accountId, userRoles, 
                httpRequest.getRemoteAddr(), httpRequest.getHeader("User-Agent"));

            // Check authorization
            if (!authorizationService.canPerformAdminOperations(requestingUserId, authContext) &&
                !authorizationService.canPortalUserAccessProfile(Integer.valueOf(userId), null, authContext)) {
                log.atWarn().log("Unauthorized portal user access attempt for ID: {}", userId);
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            PortalUserResponseDto response = portalUserService.getPortalUser(userId);

            log.atInfo().log("Portal user retrieved successfully for ID: {}", userId);
            return ResponseEntity.ok(response);

        } catch (RuntimeException e) {
            if (e.getMessage().contains("not found")) {
                log.atWarn().log("Portal user not found: {}", userId);
                return ResponseEntity.notFound().build();
            }
            
            log.atError().withCause(e).log("Failed to get portal user: {}", userId);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get portal users with filtering
     * GET /api/v1/portal-users
     */
    @GetMapping
    public ResponseEntity<List<PortalUserResponseDto>> getPortalUsers(
            @RequestParam(value = "email", required = false) String email,
            @RequestParam(value = "profileId", required = false) String profileId,
            @RequestParam(value = "page", defaultValue = "0") 
            @Min(value = 0, message = "Page must be non-negative") Integer page,
            @RequestParam(value = "size", defaultValue = "20") 
            @Min(value = 1, message = "Size must be at least 1")
            @Max(value = 100, message = "Size cannot exceed 100") Integer size,
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "accountId", required = false) Long accountId,
            HttpServletRequest httpRequest) {

        log.atInfo().log("Portal users list request - email: {}, profileId: {}, page: {}, size: {}", 
            email, profileId, page, size);

        try {
            // Build authorization context
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            List<String> userRoles = auth.getAuthorities().stream()
                .map(authority -> authority.getAuthority())
                .collect(java.util.stream.Collectors.toList());
            
            AuthorizationContextDto authContext = authorizationService.buildAuthorizationContext(
                userId, accountId, userRoles, 
                httpRequest.getRemoteAddr(), httpRequest.getHeader("User-Agent"));

            // Check authorization - only admins can list all portal users
            if (!authorizationService.canPerformAdminOperations(userId, authContext)) {
                log.atWarn().log("Unauthorized portal users list attempt from userId: {}", userId);
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            List<PortalUserResponseDto> response = portalUserService.getPortalUsers(email, profileId, page, size);

            log.atInfo().log("Portal users retrieved successfully. Count: {}", response.size());
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to get portal users");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Assign portal user to profile
     * POST /api/v1/portal-users/{userId}/assign
     */
    @PostMapping("/{userId}/assign")
    public ResponseEntity<PortalUserResponseDto> assignPortalUserToProfile(
            @PathVariable("userId") @NotBlank String userId,
            @RequestParam("profileId") @NotBlank String profileId,
            @RequestParam(value = "role", defaultValue = "admin") String role,
            @RequestParam(value = "requestingUserId", required = false) Long requestingUserId,
            @RequestParam(value = "accountId", required = false) Long accountId,
            HttpServletRequest httpRequest) {

        log.atInfo().log("Portal user assignment request - userId: {}, profileId: {}, role: {}", 
            userId, profileId, role);

        try {
            // Build authorization context
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            List<String> userRoles = auth.getAuthorities().stream()
                .map(authority -> authority.getAuthority())
                .collect(java.util.stream.Collectors.toList());
            
            AuthorizationContextDto authContext = authorizationService.buildAuthorizationContext(
                requestingUserId, accountId, userRoles, 
                httpRequest.getRemoteAddr(), httpRequest.getHeader("User-Agent"));

            // Check authorization
            if (!authorizationService.canModifyProfile(requestingUserId, accountId, profileId, authContext)) {
                log.atWarn().log("Unauthorized portal user assignment attempt");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            PortalUserResponseDto response = portalUserService.assignPortalUserToProfile(
                profileId, Integer.valueOf(userId), role);

            log.atInfo().log("Portal user assigned successfully");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to assign portal user");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Update portal user
     * PUT /api/v1/portal-users/{userId}
     */
    @PutMapping("/{userId}")
    public ResponseEntity<PortalUserResponseDto> updatePortalUser(
            @PathVariable("userId") @NotBlank String userId,
            @Valid @RequestBody PortalUserRequestDto request,
            @RequestParam(value = "requestingUserId", required = false) Long requestingUserId,
            @RequestParam(value = "accountId", required = false) Long accountId,
            HttpServletRequest httpRequest) {

        log.atInfo().log("Portal user update request for ID: {}", userId);

        try {
            // Build authorization context
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            List<String> userRoles = auth.getAuthorities().stream()
                .map(authority -> authority.getAuthority())
                .collect(java.util.stream.Collectors.toList());
            
            AuthorizationContextDto authContext = authorizationService.buildAuthorizationContext(
                requestingUserId, accountId, userRoles, 
                httpRequest.getRemoteAddr(), httpRequest.getHeader("User-Agent"));

            // Check authorization
            if (!authorizationService.canPerformAdminOperations(requestingUserId, authContext) &&
                !userId.equals(requestingUserId.toString())) {
                log.atWarn().log("Unauthorized portal user update attempt for ID: {}", userId);
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            PortalUserResponseDto response = portalUserService.updatePortalUser(userId, request);

            log.atInfo().log("Portal user updated successfully for ID: {}", userId);
            return ResponseEntity.ok(response);

        } catch (RuntimeException e) {
            if (e.getMessage().contains("not found")) {
                log.atWarn().log("Portal user not found for update: {}", userId);
                return ResponseEntity.notFound().build();
            }
            
            log.atError().withCause(e).log("Failed to update portal user: {}", userId);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Delete portal user
     * DELETE /api/v1/portal-users/{userId}
     */
    @DeleteMapping("/{userId}")
    public ResponseEntity<Void> deletePortalUser(
            @PathVariable("userId") @NotBlank String userId,
            @RequestParam(value = "requestingUserId", required = false) Long requestingUserId,
            @RequestParam(value = "accountId", required = false) Long accountId,
            HttpServletRequest httpRequest) {

        log.atInfo().log("Portal user deletion request for ID: {}", userId);

        try {
            // Build authorization context
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            List<String> userRoles = auth.getAuthorities().stream()
                .map(authority -> authority.getAuthority())
                .collect(java.util.stream.Collectors.toList());
            
            AuthorizationContextDto authContext = authorizationService.buildAuthorizationContext(
                requestingUserId, accountId, userRoles, 
                httpRequest.getRemoteAddr(), httpRequest.getHeader("User-Agent"));

            // Check authorization - only admins can delete portal users
            if (!authorizationService.canPerformAdminOperations(requestingUserId, authContext)) {
                log.atWarn().log("Unauthorized portal user deletion attempt for ID: {}", userId);
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            portalUserService.deletePortalUser(userId);

            log.atInfo().log("Portal user deleted successfully for ID: {}", userId);
            return ResponseEntity.noContent().build();

        } catch (RuntimeException e) {
            if (e.getMessage().contains("not found")) {
                log.atWarn().log("Portal user not found for deletion: {}", userId);
                return ResponseEntity.notFound().build();
            }
            
            log.atError().withCause(e).log("Failed to delete portal user: {}", userId);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get portal user activity
     * GET /api/v1/portal-users/activity
     */
    @GetMapping("/activity")
    public ResponseEntity<List<PortalUserActivityDto>> getPortalUserActivity(
            @RequestParam(value = "userId", required = false) String userId,
            @RequestParam(value = "profileId", required = false) String profileId,
            @RequestParam(value = "activityType", required = false) String activityType,
            @RequestParam(value = "page", defaultValue = "0") 
            @Min(value = 0, message = "Page must be non-negative") Integer page,
            @RequestParam(value = "size", defaultValue = "50") 
            @Min(value = 1, message = "Size must be at least 1")
            @Max(value = 100, message = "Size cannot exceed 100") Integer size,
            @RequestParam(value = "requestingUserId", required = false) Long requestingUserId,
            @RequestParam(value = "accountId", required = false) Long accountId,
            HttpServletRequest httpRequest) {

        log.atInfo().log("Portal user activity request - userId: {}, profileId: {}, type: {}", 
            userId, profileId, activityType);

        try {
            // Build authorization context
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            List<String> userRoles = auth.getAuthorities().stream()
                .map(authority -> authority.getAuthority())
                .collect(java.util.stream.Collectors.toList());
            
            AuthorizationContextDto authContext = authorizationService.buildAuthorizationContext(
                requestingUserId, accountId, userRoles, 
                httpRequest.getRemoteAddr(), httpRequest.getHeader("User-Agent"));

            // Check authorization
            if (!authorizationService.canPerformAdminOperations(requestingUserId, authContext) &&
                (profileId == null || !authorizationService.canAccessProfile(requestingUserId, accountId, profileId, authContext))) {
                log.atWarn().log("Unauthorized portal user activity access attempt");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            List<PortalUserActivityDto> response = portalUserService.getPortalUserActivity(
                userId, profileId, activityType, page, size);

            log.atInfo().log("Portal user activity retrieved successfully. Count: {}", response.size());
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to get portal user activity");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Send invitation email to portal user
     * POST /api/v1/portal-users/{userId}/invite
     */
    @PostMapping("/{userId}/invite")
    public ResponseEntity<Void> sendInvitationEmail(
            @PathVariable("userId") @NotBlank String userId,
            @RequestParam(value = "profileId", required = false) String profileId,
            @RequestParam(value = "requestingUserId", required = false) Long requestingUserId,
            @RequestParam(value = "accountId", required = false) Long accountId,
            HttpServletRequest httpRequest) {

        log.atInfo().log("Portal user invitation request for ID: {}", userId);

        try {
            // Build authorization context
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            List<String> userRoles = auth.getAuthorities().stream()
                .map(authority -> authority.getAuthority())
                .collect(java.util.stream.Collectors.toList());
            
            AuthorizationContextDto authContext = authorizationService.buildAuthorizationContext(
                requestingUserId, accountId, userRoles, 
                httpRequest.getRemoteAddr(), httpRequest.getHeader("User-Agent"));

            // Check authorization
            if (!authorizationService.canPerformAdminOperations(requestingUserId, authContext)) {
                log.atWarn().log("Unauthorized portal user invitation attempt for ID: {}", userId);
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            boolean sent = portalUserService.sendInvitationEmail(userId, profileId);

            if (sent) {
                log.atInfo().log("Portal user invitation sent successfully for ID: {}", userId);
                return ResponseEntity.ok().build();
            } else {
                log.atWarn().log("Failed to send portal user invitation for ID: {}", userId);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }

        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to send portal user invitation: {}", userId);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get portal user statistics
     * GET /api/v1/portal-users/statistics
     */
    @GetMapping("/statistics")
    public ResponseEntity<PortalUserResponseDto.PortalUserStatsDto> getPortalUserStatistics(
            @RequestParam(value = "profileId", required = false) String profileId,
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "accountId", required = false) Long accountId,
            HttpServletRequest httpRequest) {

        log.atInfo().log("Portal user statistics request for profileId: {}", profileId);

        try {
            // Build authorization context
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            List<String> userRoles = auth.getAuthorities().stream()
                .map(authority -> authority.getAuthority())
                .collect(java.util.stream.Collectors.toList());
            
            AuthorizationContextDto authContext = authorizationService.buildAuthorizationContext(
                userId, accountId, userRoles, 
                httpRequest.getRemoteAddr(), httpRequest.getHeader("User-Agent"));

            // Check authorization
            if (!authorizationService.canPerformAdminOperations(userId, authContext)) {
                log.atWarn().log("Unauthorized portal user statistics access attempt");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            PortalUserResponseDto.PortalUserStatsDto response = portalUserService.getPortalUserStatistics(profileId);

            log.atInfo().log("Portal user statistics retrieved successfully");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to get portal user statistics");
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Health check endpoint for portal user service
     * GET /api/v1/portal-users/health
     */
    @GetMapping("/health")
    public ResponseEntity<String> healthCheck() {
        return ResponseEntity.ok("Portal user service is healthy");
    }

    // Helper methods

    private String extractEmailDomain(String email) {
        if (email == null || !email.contains("@")) {
            return "unknown";
        }
        return email.substring(email.lastIndexOf("@") + 1);
    }
}
