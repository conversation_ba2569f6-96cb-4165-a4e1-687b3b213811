package com.gumtree.tns.identityverification.dao.repository;

import com.gumtree.tns.identityverification.dao.model.GbgProfile;
import com.gumtree.tns.identityverification.dao.model.GbgRepresentative;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository for GBG Representative operations
 */
@Repository
public interface GbgRepresentativeRepository extends JpaRepository<GbgRepresentative, UUID> {

    /**
     * Find representative by GBG UUID
     */
    Optional<GbgRepresentative> findByGbgRepresentativeUuid(String gbgRepresentativeUuid);

    /**
     * Find representatives by profile
     */
    List<GbgRepresentative> findByGbgProfileOrderByCreatedAtDesc(GbgProfile gbgProfile);

    /**
     * Find representatives by profile UUID
     */
    @Query("SELECT r FROM GbgRepresentative r WHERE r.gbgProfile.gbgProfileUuid = :profileUuid ORDER BY r.createdAt DESC")
    List<GbgRepresentative> findByProfileUuid(@Param("profileUuid") String profileUuid);

    /**
     * Find representative by customer reference
     */
    Optional<GbgRepresentative> findByCustomerReference(String customerReference);

    /**
     * Find representatives by email
     */
    List<GbgRepresentative> findByEmailOrderByCreatedAtDesc(String email);

    /**
     * Find representatives by full name (case-insensitive)
     */
    @Query("SELECT r FROM GbgRepresentative r WHERE LOWER(r.fullName) LIKE LOWER(CONCAT('%', :fullName, '%')) ORDER BY r.createdAt DESC")
    List<GbgRepresentative> findByFullNameContainingIgnoreCase(@Param("fullName") String fullName);

    /**
     * Find representatives by first and last name
     */
    @Query("SELECT r FROM GbgRepresentative r WHERE " +
           "(:firstName IS NULL OR LOWER(r.firstName) LIKE LOWER(CONCAT('%', :firstName, '%'))) AND " +
           "(:lastName IS NULL OR LOWER(r.lastName) LIKE LOWER(CONCAT('%', :lastName, '%'))) " +
           "ORDER BY r.createdAt DESC")
    List<GbgRepresentative> findByFirstNameAndLastName(@Param("firstName") String firstName, 
                                                      @Param("lastName") String lastName);

    /**
     * Find representatives by company flag
     */
    List<GbgRepresentative> findByIsCompanyOrderByCreatedAtDesc(Boolean isCompany);

    /**
     * Find representatives with pagination and filtering
     */
    @Query("SELECT r FROM GbgRepresentative r WHERE " +
           "(:isCompany IS NULL OR r.isCompany = :isCompany) AND " +
           "(:customerReference IS NULL OR r.customerReference LIKE %:customerReference%) AND " +
           "(:profileId IS NULL OR r.gbgProfile.gbgProfileUuid = :profileId) AND " +
           "(:email IS NULL OR LOWER(r.email) LIKE LOWER(CONCAT('%', :email, '%')))")
    Page<GbgRepresentative> findRepresentativesWithFilters(
        @Param("isCompany") Boolean isCompany,
        @Param("customerReference") String customerReference,
        @Param("profileId") String profileId,
        @Param("email") String email,
        Pageable pageable);

    /**
     * Find representatives by representative types (using JSON contains)
     */
    @Query(value = "SELECT * FROM gbg_representatives WHERE representative_types::jsonb ? :type ORDER BY created_at DESC", 
           nativeQuery = true)
    List<GbgRepresentative> findByRepresentativeType(@Param("type") String type);

    /**
     * Find representatives with multiple types
     */
    @Query(value = "SELECT * FROM gbg_representatives WHERE representative_types::jsonb ?| array[:types] ORDER BY created_at DESC", 
           nativeQuery = true)
    List<GbgRepresentative> findByRepresentativeTypes(@Param("types") String[] types);

    /**
     * Find representatives by nationality
     */
    List<GbgRepresentative> findByNationalityOrderByCreatedAtDesc(String nationality);

    /**
     * Find representatives by country of residence
     */
    List<GbgRepresentative> findByCountryOfResidenceOrderByCreatedAtDesc(String countryOfResidence);

    /**
     * Find representatives by job title (case-insensitive)
     */
    @Query("SELECT r FROM GbgRepresentative r WHERE LOWER(r.jobTitle) LIKE LOWER(CONCAT('%', :jobTitle, '%')) ORDER BY r.createdAt DESC")
    List<GbgRepresentative> findByJobTitleContainingIgnoreCase(@Param("jobTitle") String jobTitle);

    /**
     * Count representatives by profile
     */
    Long countByGbgProfile(GbgProfile gbgProfile);

    /**
     * Count representatives by profile UUID
     */
    @Query("SELECT COUNT(r) FROM GbgRepresentative r WHERE r.gbgProfile.gbgProfileUuid = :profileUuid")
    Long countByProfileUuid(@Param("profileUuid") String profileUuid);

    /**
     * Find representatives with verifications
     */
    @Query("SELECT DISTINCT r FROM GbgRepresentative r LEFT JOIN FETCH r.verifications WHERE r.gbgProfile = :profile")
    List<GbgRepresentative> findByGbgProfileWithVerifications(@Param("profile") GbgProfile profile);

    /**
     * Find representatives without verifications
     */
    @Query("SELECT r FROM GbgRepresentative r WHERE r.gbgProfile = :profile AND r.verifications IS EMPTY")
    List<GbgRepresentative> findByGbgProfileWithoutVerifications(@Param("profile") GbgProfile profile);

    /**
     * Check if representative UUID exists
     */
    boolean existsByGbgRepresentativeUuid(String gbgRepresentativeUuid);

    /**
     * Check if customer reference exists
     */
    boolean existsByCustomerReference(String customerReference);

    /**
     * Find representatives by date of birth year
     */
    @Query("SELECT r FROM GbgRepresentative r WHERE YEAR(r.dateOfBirth) = :year ORDER BY r.createdAt DESC")
    List<GbgRepresentative> findByBirthYear(@Param("year") Integer year);

    /**
     * Find representatives by personal number
     */
    Optional<GbgRepresentative> findByPersonalNumber(String personalNumber);
}
