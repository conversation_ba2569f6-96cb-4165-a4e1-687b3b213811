package com.gumtree.tns.identityverification.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * DTO for company search requests
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchRequestDto {

    @NotBlank(message = "Search type is required")
    private String type; // "company"

    @NotBlank(message = "Company name is required")
    private String name;

    private String vatNumber;
    
    private String companyReference;

    @Valid
    @NotNull(message = "Address is required")
    private AddressDto address;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AddressDto {
        private String line1;
        private String line2;
        
        @NotBlank(message = "Country code is required")
        private String countryCode; // ISO 3166-1 alpha-2
        
        private String town;
        
        @NotBlank(message = "City is required")
        private String city;
        
        private String postCode;
        private String province;
        private String state;
    }
}
