package com.gumtree.tns.identityverification.service.impl;

import com.gumtree.tns.identityverification.dao.model.GbgAnalysisResult;
import com.gumtree.tns.identityverification.dao.model.GbgChecklist;
import com.gumtree.tns.identityverification.dao.model.GbgProfile;
import com.gumtree.tns.identityverification.dao.repository.GbgAnalysisResultRepository;
import com.gumtree.tns.identityverification.dao.repository.GbgChecklistRepository;
import com.gumtree.tns.identityverification.dao.repository.GbgProfileRepository;
import com.gumtree.tns.identityverification.gateway.client.GbgApiClient;
import com.gumtree.tns.identityverification.gateway.client.dto.GbgApiClientDtos.*;
import com.gumtree.tns.identityverification.service.GbgAnalysisService;
import com.gumtree.tns.identityverification.service.dto.AnalysisResponseDto;
import com.gumtree.tns.identityverification.service.dto.ChecklistResponseDto;
import com.gumtree.tns.identityverification.service.mapper.GbgAnalysisMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Implementation of GBG analysis service
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GbgAnalysisServiceImpl implements GbgAnalysisService {

    private final GbgApiClient gbgApiClient;
    private final GbgProfileRepository profileRepository;
    private final GbgAnalysisResultRepository analysisResultRepository;
    private final GbgChecklistRepository checklistRepository;
    private final GbgAnalysisMapper analysisMapper;

    @Override
    @Transactional(readOnly = true)
    public List<AnalysisResponseDto> getProfileAnalysis(String profileUuid) {
        log.atInfo().log("Getting analysis for profile: {}", profileUuid);

        try {
            // Get profile
            GbgProfile profile = getProfileByUuid(profileUuid);
            
            // Get cached analysis results
            List<GbgAnalysisResult> cachedResults = analysisResultRepository.findByGbgProfileOrderByCreatedAtDesc(profile);
            
            if (!cachedResults.isEmpty()) {
                log.atInfo().log("Returning cached analysis results for profile: {}", profileUuid);
                return cachedResults.stream()
                    .map(analysisMapper::toAnalysisResponseDto)
                    .collect(Collectors.toList());
            }
            
            // If no cached results, fetch from GBG
            return refreshProfileAnalysis(profileUuid);
            
        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to get analysis for profile: {}", profileUuid);
            throw new RuntimeException("Failed to retrieve profile analysis", e);
        }
    }

    @Override
    @Transactional
    public List<AnalysisResponseDto> refreshProfileAnalysis(String profileUuid) {
        log.atInfo().log("Refreshing analysis for profile: {}", profileUuid);

        try {
            // Get profile
            GbgProfile profile = getProfileByUuid(profileUuid);
            
            // Call GBG API
            GbgApiResponse<List<GbgAnalysisResponse>> gbgResponse = gbgApiClient.getProfileAnalysis(profileUuid);
            
            if (gbgResponse.getData() == null || gbgResponse.getData().isEmpty()) {
                log.atWarn().log("No analysis data returned from GBG for profile: {}", profileUuid);
                return List.of();
            }
            
            // Save analysis results
            List<GbgAnalysisResult> analysisResults = gbgResponse.getData().stream()
                .map(gbgAnalysis -> {
                    GbgAnalysisResult result = analysisMapper.toAnalysisResult(gbgAnalysis, profile);
                    return analysisResultRepository.save(result);
                })
                .collect(Collectors.toList());
            
            log.atInfo().log("Saved {} analysis results for profile: {}", analysisResults.size(), profileUuid);
            
            return analysisResults.stream()
                .map(analysisMapper::toAnalysisResponseDto)
                .collect(Collectors.toList());
            
        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to refresh analysis for profile: {}", profileUuid);
            throw new RuntimeException("Failed to refresh profile analysis", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<ChecklistResponseDto> getProfileChecklists(String profileUuid) {
        log.atInfo().log("Getting checklists for profile: {}", profileUuid);

        try {
            // Get profile
            GbgProfile profile = getProfileByUuid(profileUuid);
            
            // Get cached checklist results
            List<GbgChecklist> cachedChecklists = checklistRepository.findByGbgProfileOrderByCreatedAtDesc(profile);
            
            if (!cachedChecklists.isEmpty()) {
                log.atInfo().log("Returning cached checklist results for profile: {}", profileUuid);
                return cachedChecklists.stream()
                    .map(analysisMapper::toChecklistResponseDto)
                    .collect(Collectors.toList());
            }
            
            // If no cached results, fetch from GBG
            return refreshProfileChecklists(profileUuid);
            
        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to get checklists for profile: {}", profileUuid);
            throw new RuntimeException("Failed to retrieve profile checklists", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public ChecklistResponseDto getProfileChecklist(String profileUuid, String checklistId) {
        log.atInfo().log("Getting checklist {} for profile: {}", checklistId, profileUuid);

        try {
            // Get profile
            GbgProfile profile = getProfileByUuid(profileUuid);
            
            // Find specific checklist
            Optional<GbgChecklist> checklist = checklistRepository.findByGbgProfileAndGbgChecklistId(
                profile, Integer.valueOf(checklistId));
            
            if (checklist.isPresent()) {
                return analysisMapper.toChecklistResponseDto(checklist.get());
            }
            
            // If not found locally, try to get from GBG
            GbgApiResponse<GbgChecklistResponse> gbgResponse = gbgApiClient.getProfileChecklist(profileUuid, checklistId);
            
            if (gbgResponse.getData() != null) {
                // Save and return
                GbgChecklist savedChecklist = analysisMapper.toChecklistEntity(gbgResponse.getData(), profile);
                savedChecklist = checklistRepository.save(savedChecklist);
                return analysisMapper.toChecklistResponseDto(savedChecklist);
            }
            
            throw new RuntimeException("Checklist not found: " + checklistId);
            
        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to get checklist {} for profile: {}", checklistId, profileUuid);
            throw new RuntimeException("Failed to retrieve checklist", e);
        }
    }

    @Override
    @Transactional
    public List<ChecklistResponseDto> refreshProfileChecklists(String profileUuid) {
        log.atInfo().log("Refreshing checklists for profile: {}", profileUuid);

        try {
            // Get profile
            GbgProfile profile = getProfileByUuid(profileUuid);
            
            // Call GBG API
            GbgApiResponse<List<GbgChecklistResponse>> gbgResponse = gbgApiClient.getProfileChecklists(
                profileUuid, null, null, null, null, null);
            
            if (gbgResponse.getData() == null || gbgResponse.getData().isEmpty()) {
                log.atWarn().log("No checklist data returned from GBG for profile: {}", profileUuid);
                return List.of();
            }
            
            // Save checklist results
            List<GbgChecklist> checklists = gbgResponse.getData().stream()
                .map(gbgChecklist -> {
                    GbgChecklist checklist = analysisMapper.toChecklistEntity(gbgChecklist, profile);
                    return checklistRepository.save(checklist);
                })
                .collect(Collectors.toList());
            
            log.atInfo().log("Saved {} checklists for profile: {}", checklists.size(), profileUuid);
            
            return checklists.stream()
                .map(analysisMapper::toChecklistResponseDto)
                .collect(Collectors.toList());
            
        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to refresh checklists for profile: {}", profileUuid);
            throw new RuntimeException("Failed to refresh profile checklists", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public AnalysisResponseDto.RiskSummaryDto getProfileRiskSummary(String profileUuid) {
        log.atInfo().log("Getting risk summary for profile: {}", profileUuid);

        try {
            List<AnalysisResponseDto> analyses = getProfileAnalysis(profileUuid);
            
            if (analyses.isEmpty()) {
                return AnalysisResponseDto.RiskSummaryDto.builder()
                    .totalScore(0)
                    .riskCategory("Unknown")
                    .requiresManualReview(true)
                    .reviewReason("No analysis data available")
                    .build();
            }
            
            // Calculate overall risk from all analyses
            return analysisMapper.calculateRiskSummary(analyses);
            
        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to get risk summary for profile: {}", profileUuid);
            throw new RuntimeException("Failed to calculate risk summary", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public boolean isProfileCompliant(String profileUuid) {
        log.atInfo().log("Checking compliance for profile: {}", profileUuid);

        try {
            ChecklistResponseDto.ComplianceSummaryDto summary = getComplianceSummary(profileUuid);
            return summary.getIsCompliant();
            
        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to check compliance for profile: {}", profileUuid);
            return false; // Default to non-compliant on error
        }
    }

    @Override
    @Transactional(readOnly = true)
    public ChecklistResponseDto.ComplianceSummaryDto getComplianceSummary(String profileUuid) {
        log.atInfo().log("Getting compliance summary for profile: {}", profileUuid);

        try {
            List<ChecklistResponseDto> checklists = getProfileChecklists(profileUuid);
            
            if (checklists.isEmpty()) {
                return ChecklistResponseDto.ComplianceSummaryDto.builder()
                    .isCompliant(false)
                    .complianceStatus("incomplete")
                    .complianceScore(0)
                    .missingRequirements(List.of("No checklists available"))
                    .build();
            }
            
            // Calculate compliance from all checklists
            return analysisMapper.calculateComplianceSummary(checklists);
            
        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to get compliance summary for profile: {}", profileUuid);
            throw new RuntimeException("Failed to calculate compliance summary", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<AnalysisResponseDto> getAnalysisHistory(String profileUuid, Integer limit) {
        log.atInfo().log("Getting analysis history for profile: {}, limit: {}", profileUuid, limit);

        try {
            GbgProfile profile = getProfileByUuid(profileUuid);
            
            List<GbgAnalysisResult> results = limit != null 
                ? analysisResultRepository.findTopNByGbgProfileOrderByCreatedAtDesc(profile, limit)
                : analysisResultRepository.findByGbgProfileOrderByCreatedAtDesc(profile);
            
            return results.stream()
                .map(analysisMapper::toAnalysisResponseDto)
                .collect(Collectors.toList());
            
        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to get analysis history for profile: {}", profileUuid);
            throw new RuntimeException("Failed to retrieve analysis history", e);
        }
    }

    private GbgProfile getProfileByUuid(String profileUuid) {
        return profileRepository.findByGbgProfileUuid(profileUuid)
            .orElseThrow(() -> new RuntimeException("Profile not found: " + profileUuid));
    }
}
