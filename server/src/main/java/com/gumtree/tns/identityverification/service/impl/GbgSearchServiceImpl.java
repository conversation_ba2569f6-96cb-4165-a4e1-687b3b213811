package com.gumtree.tns.identityverification.service.impl;

import com.gumtree.tns.identityverification.dao.model.GbgSearchHistory;
import com.gumtree.tns.identityverification.dao.repository.GbgSearchHistoryRepository;
import com.gumtree.tns.identityverification.gateway.client.GbgApiClient;
import com.gumtree.tns.identityverification.gateway.client.dto.GbgApiClientDtos.*;
import com.gumtree.tns.identityverification.service.GbgSearchService;
import com.gumtree.tns.identityverification.service.dto.SearchRequestDto;
import com.gumtree.tns.identityverification.service.dto.SearchResponseDto;
import com.gumtree.tns.identityverification.service.mapper.GbgSearchMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Implementation of GBG search service
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GbgSearchServiceImpl implements GbgSearchService {

    private final GbgApiClient gbgApiClient;
    private final GbgSearchHistoryRepository searchHistoryRepository;
    private final GbgSearchMapper searchMapper;

    @Override
    @Transactional
    public SearchResponseDto searchCompanies(SearchRequestDto searchRequest, Long userId, Long accountId) {
        log.atInfo().log("Searching companies with criteria: name={}, countryCode={}", 
            searchRequest.getName(), searchRequest.getAddress().getCountryCode());

        try {
            // Convert DTO to GBG API request
            GbgSearchRequest gbgRequest = searchMapper.toGbgSearchRequest(searchRequest);
            
            // Call GBG API
            GbgSearchResponse gbgResponse = gbgApiClient.searchCompanies(gbgRequest);
            
            // Save search history
            GbgSearchHistory searchHistory = GbgSearchHistory.builder()
                .lookupId(gbgResponse.getLookupId())
                .searchType("company")
                .searchCriteria(searchMapper.toJsonString(searchRequest))
                .searchResults(searchMapper.toJsonString(gbgResponse))
                .resultCount(gbgResponse.getData() != null ? gbgResponse.getData().size() : 0)
                .createdByUserId(userId)
                .createdByAccountId(accountId)
                .createdAt(LocalDateTime.now())
                .build();
            
            searchHistoryRepository.save(searchHistory);
            
            // Convert response to DTO
            SearchResponseDto response = searchMapper.toSearchResponseDto(gbgResponse);
            response.setSearchedAt(LocalDateTime.now());
            
            log.atInfo().log("Company search completed. LookupId: {}, Results: {}", 
                gbgResponse.getLookupId(), response.getTotalResults());
            
            return response;
            
        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to search companies");
            throw new RuntimeException("Company search failed", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public SearchResponseDto getSearchHistory(Long userId, Long accountId, Integer limit) {
        log.atInfo().log("Getting search history for userId: {}, accountId: {}, limit: {}", 
            userId, accountId, limit);

        try {
            List<GbgSearchHistory> searchHistory;
            
            if (userId != null) {
                searchHistory = searchHistoryRepository.findByCreatedByUserIdOrderByCreatedAtDesc(userId, limit);
            } else if (accountId != null) {
                searchHistory = searchHistoryRepository.findByCreatedByAccountIdOrderByCreatedAtDesc(accountId, limit);
            } else {
                throw new IllegalArgumentException("Either userId or accountId must be provided");
            }
            
            // Convert to response DTO
            return searchMapper.toSearchHistoryResponseDto(searchHistory);
            
        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to get search history");
            throw new RuntimeException("Failed to retrieve search history", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public SearchResponseDto getSearchResults(Integer lookupId) {
        log.atInfo().log("Getting search results for lookupId: {}", lookupId);

        try {
            Optional<GbgSearchHistory> searchHistory = searchHistoryRepository.findByLookupId(lookupId);
            
            if (searchHistory.isEmpty()) {
                throw new RuntimeException("Search results not found for lookupId: " + lookupId);
            }
            
            // Parse stored results and convert to DTO
            return searchMapper.parseSearchResults(searchHistory.get());
            
        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to get search results for lookupId: {}", lookupId);
            throw new RuntimeException("Failed to retrieve search results", e);
        }
    }
}
