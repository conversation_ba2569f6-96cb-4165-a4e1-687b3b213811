package com.gumtree.tns.identityverification.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * DTO for representative responses
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RepresentativeResponseDto {

    private String id;
    private String profileId;
    private String customerReference;
    private Boolean isCompany;
    private List<String> types;
    private String fullName;
    private String professionalProfile;
    private String country;
    private String countryCode;
    private String countryOfResidence;
    private String residenceCountryCode;
    private String personalNumber;
    private String firstName;
    private String lastName;
    private String middleName;
    private LocalDate dateOfBirth;
    private String email;
    private String jobTitle;
    private AddressDto address;
    private String countryOfBirth;
    private String birthCountryCode;
    private String nationality;
    private PersonTypeDetailsDto personTypeDetails;
    private Boolean copyAddress;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private List<VerificationSummaryDto> verifications;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AddressDto {
        private String apartmentNumber;
        private String address1;
        private String address2;
        private String city;
        private String state;
        private String postCode;
        private String countryCode;
        private String fullAddress;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PersonTypeDetailsDto {
        private String appointmentDate;
        private Boolean majority;
        private Boolean governing;
        private Boolean responsibility;
        private String majorityPercentage;
        private String specificOwnershipPercentage;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VerificationSummaryDto {
        private String verificationType;
        private String provider;
        private String state;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
        private Boolean isCompleted;
        private String resultSummary;
    }
}
