package com.gumtree.tns.identityverification.dao.repository;

import com.gumtree.tns.identityverification.dao.model.GbgRepresentative;
import com.gumtree.tns.identityverification.dao.model.GbgRepresentativeVerification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository for GBG Representative Verification operations
 */
@Repository
public interface GbgRepresentativeVerificationRepository extends JpaRepository<GbgRepresentativeVerification, UUID> {

    /**
     * Find verifications by representative
     */
    List<GbgRepresentativeVerification> findByGbgRepresentativeOrderByCreatedAtDesc(GbgRepresentative representative);

    /**
     * Find verifications by representative UUID
     */
    @Query("SELECT v FROM GbgRepresentativeVerification v WHERE v.gbgRepresentative.gbgRepresentativeUuid = :representativeUuid ORDER BY v.createdAt DESC")
    List<GbgRepresentativeVerification> findByRepresentativeUuid(@Param("representativeUuid") String representativeUuid);

    /**
     * Find verifications by verification type
     */
    List<GbgRepresentativeVerification> findByVerificationTypeOrderByCreatedAtDesc(
        GbgRepresentativeVerification.VerificationType verificationType);

    /**
     * Find verifications by provider
     */
    List<GbgRepresentativeVerification> findByProviderOrderByCreatedAtDesc(
        GbgRepresentativeVerification.Provider provider);

    /**
     * Find verifications by state
     */
    List<GbgRepresentativeVerification> findByStateOrderByCreatedAtDesc(
        GbgRepresentativeVerification.VerificationState state);

    /**
     * Find verification by session ID
     */
    Optional<GbgRepresentativeVerification> findBySessionId(String sessionId);

    /**
     * Find verifications by representative and type
     */
    List<GbgRepresentativeVerification> findByGbgRepresentativeAndVerificationTypeOrderByCreatedAtDesc(
        GbgRepresentative representative, 
        GbgRepresentativeVerification.VerificationType verificationType);

    /**
     * Find verifications by representative UUID and type
     */
    @Query("SELECT v FROM GbgRepresentativeVerification v WHERE " +
           "v.gbgRepresentative.gbgRepresentativeUuid = :representativeUuid AND " +
           "v.verificationType = :verificationType ORDER BY v.createdAt DESC")
    List<GbgRepresentativeVerification> findByRepresentativeUuidAndType(
        @Param("representativeUuid") String representativeUuid,
        @Param("verificationType") GbgRepresentativeVerification.VerificationType verificationType);

    /**
     * Find latest verification by representative and type
     */
    @Query("SELECT v FROM GbgRepresentativeVerification v WHERE " +
           "v.gbgRepresentative = :representative AND " +
           "v.verificationType = :verificationType " +
           "ORDER BY v.createdAt DESC")
    Optional<GbgRepresentativeVerification> findLatestByRepresentativeAndType(
        @Param("representative") GbgRepresentative representative,
        @Param("verificationType") GbgRepresentativeVerification.VerificationType verificationType);

    /**
     * Find active (open) verifications by representative
     */
    @Query("SELECT v FROM GbgRepresentativeVerification v WHERE " +
           "v.gbgRepresentative = :representative AND " +
           "v.state = 'OPEN' ORDER BY v.createdAt DESC")
    List<GbgRepresentativeVerification> findActiveVerificationsByRepresentative(
        @Param("representative") GbgRepresentative representative);

    /**
     * Find completed verifications by representative
     */
    @Query("SELECT v FROM GbgRepresentativeVerification v WHERE " +
           "v.gbgRepresentative = :representative AND " +
           "v.state = 'CLOSED' ORDER BY v.createdAt DESC")
    List<GbgRepresentativeVerification> findCompletedVerificationsByRepresentative(
        @Param("representative") GbgRepresentative representative);

    /**
     * Find failed verifications by representative
     */
    @Query("SELECT v FROM GbgRepresentativeVerification v WHERE " +
           "v.gbgRepresentative = :representative AND " +
           "v.state = 'FAILED' ORDER BY v.createdAt DESC")
    List<GbgRepresentativeVerification> findFailedVerificationsByRepresentative(
        @Param("representative") GbgRepresentative representative);

    /**
     * Count verifications by representative
     */
    Long countByGbgRepresentative(GbgRepresentative representative);

    /**
     * Count verifications by representative and state
     */
    Long countByGbgRepresentativeAndState(GbgRepresentative representative, 
                                         GbgRepresentativeVerification.VerificationState state);

    /**
     * Count verifications by representative and type
     */
    Long countByGbgRepresentativeAndVerificationType(GbgRepresentative representative, 
                                                    GbgRepresentativeVerification.VerificationType verificationType);

    /**
     * Find verifications created within a date range
     */
    @Query("SELECT v FROM GbgRepresentativeVerification v WHERE " +
           "v.createdAt >= :startDate AND v.createdAt <= :endDate ORDER BY v.createdAt DESC")
    List<GbgRepresentativeVerification> findByCreatedAtBetween(@Param("startDate") LocalDateTime startDate, 
                                                              @Param("endDate") LocalDateTime endDate);

    /**
     * Find verifications by provider and state
     */
    List<GbgRepresentativeVerification> findByProviderAndStateOrderByCreatedAtDesc(
        GbgRepresentativeVerification.Provider provider,
        GbgRepresentativeVerification.VerificationState state);

    /**
     * Find verifications that need attention (open for more than specified hours)
     */
    @Query("SELECT v FROM GbgRepresentativeVerification v WHERE " +
           "v.state = 'OPEN' AND v.createdAt < :cutoffTime ORDER BY v.createdAt ASC")
    List<GbgRepresentativeVerification> findStaleOpenVerifications(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * Check if representative has any completed verifications
     */
    @Query("SELECT CASE WHEN COUNT(v) > 0 THEN true ELSE false END FROM GbgRepresentativeVerification v WHERE " +
           "v.gbgRepresentative = :representative AND v.state = 'CLOSED'")
    boolean hasCompletedVerifications(@Param("representative") GbgRepresentative representative);

    /**
     * Check if representative has verification of specific type
     */
    @Query("SELECT CASE WHEN COUNT(v) > 0 THEN true ELSE false END FROM GbgRepresentativeVerification v WHERE " +
           "v.gbgRepresentative = :representative AND v.verificationType = :verificationType")
    boolean hasVerificationOfType(@Param("representative") GbgRepresentative representative,
                                 @Param("verificationType") GbgRepresentativeVerification.VerificationType verificationType);

    /**
     * Find verifications by session token
     */
    Optional<GbgRepresentativeVerification> findBySessionToken(String sessionToken);
}
