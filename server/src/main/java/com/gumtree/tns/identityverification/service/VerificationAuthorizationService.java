package com.gumtree.tns.identityverification.service;

import com.gumtree.tns.identityverification.service.dto.AuthorizationContextDto;

/**
 * Service for handling authorization and access control for verification operations
 */
public interface VerificationAuthorizationService {

    /**
     * Check if user can initiate KYC verification
     * 
     * @param userId the user ID
     * @param context authorization context
     * @return true if authorized
     */
    boolean canInitiateKycVerification(Long userId, AuthorizationContextDto context);

    /**
     * Check if account can initiate KYB verification
     * 
     * @param accountId the account ID
     * @param initiatedByUserId the user initiating on behalf of account
     * @param context authorization context
     * @return true if authorized
     */
    boolean canInitiateKybVerification(Long accountId, Long initiatedByUserId, AuthorizationContextDto context);

    /**
     * Check if user can access profile
     * 
     * @param userId the user ID (optional)
     * @param accountId the account ID (optional)
     * @param profileUuid the GBG profile UUID
     * @param context authorization context
     * @return true if authorized
     */
    boolean canAccessProfile(Long userId, Long accountId, String profileUuid, AuthorizationContextDto context);

    /**
     * Check if user can modify profile
     * 
     * @param userId the user ID (optional)
     * @param accountId the account ID (optional)
     * @param profileUuid the GBG profile UUID
     * @param context authorization context
     * @return true if authorized
     */
    boolean canModifyProfile(Long userId, Long accountId, String profileUuid, AuthorizationContextDto context);

    /**
     * Check if user can add representatives
     * 
     * @param userId the user ID (optional)
     * @param accountId the account ID (optional)
     * @param profileUuid the GBG profile UUID
     * @param context authorization context
     * @return true if authorized
     */
    boolean canAddRepresentatives(Long userId, Long accountId, String profileUuid, AuthorizationContextDto context);

    /**
     * Check if user can access representative information
     * 
     * @param userId the user ID (optional)
     * @param accountId the account ID (optional)
     * @param representativeUuid the representative UUID
     * @param context authorization context
     * @return true if authorized
     */
    boolean canAccessRepresentative(Long userId, Long accountId, String representativeUuid, AuthorizationContextDto context);

    /**
     * Check if portal user can access profile
     * 
     * @param portalUserId the portal user ID
     * @param profileUuid the GBG profile UUID
     * @param context authorization context
     * @return true if authorized
     */
    boolean canPortalUserAccessProfile(Integer portalUserId, String profileUuid, AuthorizationContextDto context);

    /**
     * Check if portal user can upload documents
     * 
     * @param portalUserId the portal user ID
     * @param profileUuid the GBG profile UUID
     * @param context authorization context
     * @return true if authorized
     */
    boolean canPortalUserUploadDocuments(Integer portalUserId, String profileUuid, AuthorizationContextDto context);

    /**
     * Check if user can perform admin operations
     * 
     * @param userId the user ID
     * @param context authorization context
     * @return true if authorized
     */
    boolean canPerformAdminOperations(Long userId, AuthorizationContextDto context);

    /**
     * Check if user can access search functionality
     * 
     * @param userId the user ID (optional)
     * @param accountId the account ID (optional)
     * @param context authorization context
     * @return true if authorized
     */
    boolean canAccessSearch(Long userId, Long accountId, AuthorizationContextDto context);

    /**
     * Check if user can access analysis data
     * 
     * @param userId the user ID (optional)
     * @param accountId the account ID (optional)
     * @param profileUuid the GBG profile UUID
     * @param context authorization context
     * @return true if authorized
     */
    boolean canAccessAnalysis(Long userId, Long accountId, String profileUuid, AuthorizationContextDto context);

    /**
     * Check if user can trigger analysis refresh
     * 
     * @param userId the user ID (optional)
     * @param accountId the account ID (optional)
     * @param profileUuid the GBG profile UUID
     * @param context authorization context
     * @return true if authorized
     */
    boolean canRefreshAnalysis(Long userId, Long accountId, String profileUuid, AuthorizationContextDto context);

    /**
     * Validate request context and extract authorization information
     * 
     * @param userId the user ID from request
     * @param accountId the account ID from request
     * @param userRoles user roles from security context
     * @param ipAddress client IP address
     * @param userAgent client user agent
     * @return authorization context
     */
    AuthorizationContextDto buildAuthorizationContext(Long userId, Long accountId, 
                                                     java.util.List<String> userRoles,
                                                     String ipAddress, String userAgent);

    /**
     * Log authorization decision for audit
     * 
     * @param operation the operation being authorized
     * @param userId the user ID (optional)
     * @param accountId the account ID (optional)
     * @param resourceId the resource ID (optional)
     * @param authorized whether access was granted
     * @param reason reason for decision
     * @param context authorization context
     */
    void logAuthorizationDecision(String operation, Long userId, Long accountId, 
                                String resourceId, boolean authorized, String reason,
                                AuthorizationContextDto context);

    /**
     * Check rate limits for user/account
     * 
     * @param userId the user ID (optional)
     * @param accountId the account ID (optional)
     * @param operation the operation being performed
     * @param context authorization context
     * @return true if within rate limits
     */
    boolean checkRateLimits(Long userId, Long accountId, String operation, AuthorizationContextDto context);

    /**
     * User roles enum
     */
    enum UserRole {
        ADMIN("admin"),
        USER("user"),
        ACCOUNT_ADMIN("account_admin"),
        ACCOUNT_USER("account_user"),
        PORTAL_USER("portal_user"),
        READONLY("readonly");

        private final String value;

        UserRole(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static UserRole fromValue(String value) {
            for (UserRole role : values()) {
                if (role.value.equals(value)) {
                    return role;
                }
            }
            throw new IllegalArgumentException("Unknown user role: " + value);
        }
    }

    /**
     * Operations that can be authorized
     */
    enum Operation {
        INITIATE_KYC("initiate_kyc"),
        INITIATE_KYB("initiate_kyb"),
        ACCESS_PROFILE("access_profile"),
        MODIFY_PROFILE("modify_profile"),
        ADD_REPRESENTATIVES("add_representatives"),
        ACCESS_REPRESENTATIVE("access_representative"),
        UPLOAD_DOCUMENTS("upload_documents"),
        ACCESS_SEARCH("access_search"),
        ACCESS_ANALYSIS("access_analysis"),
        REFRESH_ANALYSIS("refresh_analysis"),
        ADMIN_OPERATIONS("admin_operations");

        private final String value;

        Operation(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }
}
