package com.gumtree.tns.identityverification.dao.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Entity representing GBG search history
 */
@Entity
@Table(name = "gbg_search_history")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GbgSearchHistory {

    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", updatable = false, nullable = false)
    private UUID id;

    @Column(name = "lookup_id")
    private Integer lookupId;

    @Column(name = "search_type", nullable = false)
    private String searchType;

    @Type(type = "jsonb")
    @Column(name = "search_criteria", columnDefinition = "jsonb", nullable = false)
    private String searchCriteria;

    @Type(type = "jsonb")
    @Column(name = "search_results", columnDefinition = "jsonb")
    private String searchResults;

    @Column(name = "result_count", nullable = false)
    @Builder.Default
    private Integer resultCount = 0;

    @Column(name = "created_by_user_id")
    private Long createdByUserId;

    @Column(name = "created_by_account_id")
    private Long createdByAccountId;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @PrePersist
    protected void onCreate() {
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
    }
}
