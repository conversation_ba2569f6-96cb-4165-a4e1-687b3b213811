package com.gumtree.tns.identityverification.dao.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Entity representing GBG Representative - key personnel for companies
 */
@Entity
@Table(name = "gbg_representatives")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GbgRepresentative {

    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", updatable = false, nullable = false)
    private UUID id;

    @Column(name = "gbg_representative_uuid", nullable = false, unique = true)
    private String gbgRepresentativeUuid;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "gbg_profile_id", nullable = false)
    private GbgProfile gbgProfile;

    @Column(name = "customer_reference")
    private String customerReference;

    @Column(name = "is_company", nullable = false)
    @Builder.Default
    private Boolean isCompany = false;

    @Type(type = "jsonb")
    @Column(name = "representative_types", columnDefinition = "jsonb", nullable = false)
    private String representativeTypes; // JSON array of types: director, identity, ownership, declaratory, principal, director_company

    @Column(name = "full_name")
    private String fullName;

    @Column(name = "first_name")
    private String firstName;

    @Column(name = "middle_name")
    private String middleName;

    @Column(name = "last_name")
    private String lastName;

    @Column(name = "email")
    private String email;

    @Column(name = "date_of_birth")
    private LocalDate dateOfBirth;

    @Column(name = "job_title")
    private String jobTitle;

    @Column(name = "professional_profile")
    private String professionalProfile;

    @Column(name = "nationality")
    private String nationality;

    @Column(name = "country_of_birth")
    private String countryOfBirth;

    @Column(name = "birth_country_code")
    private String birthCountryCode;

    @Column(name = "country_of_residence")
    private String countryOfResidence;

    @Column(name = "residence_country_code")
    private String residenceCountryCode;

    @Column(name = "personal_number")
    private String personalNumber;

    @Type(type = "jsonb")
    @Column(name = "address_data", columnDefinition = "jsonb")
    private String addressData;

    @Type(type = "jsonb")
    @Column(name = "person_type_details", columnDefinition = "jsonb")
    private String personTypeDetails;

    @Column(name = "copy_address")
    @Builder.Default
    private Boolean copyAddress = false;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // Relationships
    @OneToMany(mappedBy = "gbgRepresentative", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<GbgRepresentativeVerification> verifications;

    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        if (createdAt == null) {
            createdAt = now;
        }
        if (updatedAt == null) {
            updatedAt = now;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    /**
     * Representative types enum
     */
    public enum RepresentativeType {
        DIRECTOR("director"),
        IDENTITY("identity"),
        OWNERSHIP("ownership"),
        DECLARATORY("declaratory"),
        PRINCIPAL("principal"),
        DIRECTOR_COMPANY("director_company");

        private final String value;

        RepresentativeType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static RepresentativeType fromValue(String value) {
            for (RepresentativeType type : values()) {
                if (type.value.equals(value)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("Unknown representative type: " + value);
        }
    }
}
