package com.gumtree.tns.identityverification.controller;

import com.gumtree.tns.identityverification.service.GbgAnalysisService;
import com.gumtree.tns.identityverification.service.dto.AnalysisResponseDto;
import com.gumtree.tns.identityverification.service.dto.ChecklistResponseDto;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * Controller for analysis and compliance operations
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/analysis")
@RequiredArgsConstructor
@Validated
public class AnalysisController {

    private final GbgAnalysisService gbgAnalysisService;
    private final MeterRegistry meterRegistry;
    private final Timer analysisTimer;

    public AnalysisController(GbgAnalysisService gbgAnalysisService, MeterRegistry meterRegistry) {
        this.gbgAnalysisService = gbgAnalysisService;
        this.meterRegistry = meterRegistry;
        this.analysisTimer = Timer.builder("analysis.requests.duration")
            .description("Duration of analysis operations")
            .register(meterRegistry);
    }

    /**
     * Get risk analysis for a profile
     * GET /api/v1/analysis/profiles/{profileUuid}/analysis
     */
    @GetMapping("/profiles/{profileUuid}/analysis")
    public ResponseEntity<List<AnalysisResponseDto>> getProfileAnalysis(
            @PathVariable("profileUuid") @NotBlank String profileUuid) {

        log.atInfo().log("Analysis request for profile: {}", profileUuid);

        return analysisTimer.recordCallable(() -> {
            try {
                Counter.builder("analysis.requests.total")
                    .tag("operation", "get_analysis")
                    .tag("profile", profileUuid)
                    .register(meterRegistry)
                    .increment();

                List<AnalysisResponseDto> analyses = gbgAnalysisService.getProfileAnalysis(profileUuid);

                Counter.builder("analysis.requests.success.total")
                    .tag("operation", "get_analysis")
                    .tag("results", String.valueOf(analyses.size()))
                    .register(meterRegistry)
                    .increment();

                log.atInfo().log("Analysis retrieved successfully for profile: {}. Results: {}", 
                    profileUuid, analyses.size());

                return ResponseEntity.ok(analyses);

            } catch (Exception e) {
                Counter.builder("analysis.requests.error.total")
                    .tag("operation", "get_analysis")
                    .tag("error", e.getClass().getSimpleName())
                    .register(meterRegistry)
                    .increment();

                log.atError().withCause(e).log("Failed to get analysis for profile: {}", profileUuid);
                throw e;
            }
        });
    }

    /**
     * Refresh analysis data from GBG
     * POST /api/v1/analysis/profiles/{profileUuid}/analysis/refresh
     */
    @PostMapping("/profiles/{profileUuid}/analysis/refresh")
    public ResponseEntity<List<AnalysisResponseDto>> refreshProfileAnalysis(
            @PathVariable("profileUuid") @NotBlank String profileUuid) {

        log.atInfo().log("Analysis refresh request for profile: {}", profileUuid);

        return analysisTimer.recordCallable(() -> {
            try {
                Counter.builder("analysis.refresh.total")
                    .tag("profile", profileUuid)
                    .register(meterRegistry)
                    .increment();

                List<AnalysisResponseDto> analyses = gbgAnalysisService.refreshProfileAnalysis(profileUuid);

                Counter.builder("analysis.refresh.success.total")
                    .tag("profile", profileUuid)
                    .tag("results", String.valueOf(analyses.size()))
                    .register(meterRegistry)
                    .increment();

                log.atInfo().log("Analysis refreshed successfully for profile: {}. Results: {}", 
                    profileUuid, analyses.size());

                return ResponseEntity.ok(analyses);

            } catch (Exception e) {
                Counter.builder("analysis.refresh.error.total")
                    .tag("profile", profileUuid)
                    .tag("error", e.getClass().getSimpleName())
                    .register(meterRegistry)
                    .increment();

                log.atError().withCause(e).log("Failed to refresh analysis for profile: {}", profileUuid);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }
        });
    }

    /**
     * Get risk summary for a profile
     * GET /api/v1/analysis/profiles/{profileUuid}/risk-summary
     */
    @GetMapping("/profiles/{profileUuid}/risk-summary")
    public ResponseEntity<AnalysisResponseDto.RiskSummaryDto> getProfileRiskSummary(
            @PathVariable("profileUuid") @NotBlank String profileUuid) {

        log.atInfo().log("Risk summary request for profile: {}", profileUuid);

        try {
            AnalysisResponseDto.RiskSummaryDto riskSummary = gbgAnalysisService.getProfileRiskSummary(profileUuid);

            log.atInfo().log("Risk summary retrieved for profile: {}. Risk: {}, Score: {}", 
                profileUuid, riskSummary.getRiskCategory(), riskSummary.getTotalScore());

            return ResponseEntity.ok(riskSummary);

        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to get risk summary for profile: {}", profileUuid);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get compliance checklists for a profile
     * GET /api/v1/analysis/profiles/{profileUuid}/checklists
     */
    @GetMapping("/profiles/{profileUuid}/checklists")
    public ResponseEntity<List<ChecklistResponseDto>> getProfileChecklists(
            @PathVariable("profileUuid") @NotBlank String profileUuid) {

        log.atInfo().log("Checklists request for profile: {}", profileUuid);

        return analysisTimer.recordCallable(() -> {
            try {
                Counter.builder("checklists.requests.total")
                    .tag("operation", "get_checklists")
                    .tag("profile", profileUuid)
                    .register(meterRegistry)
                    .increment();

                List<ChecklistResponseDto> checklists = gbgAnalysisService.getProfileChecklists(profileUuid);

                Counter.builder("checklists.requests.success.total")
                    .tag("operation", "get_checklists")
                    .tag("results", String.valueOf(checklists.size()))
                    .register(meterRegistry)
                    .increment();

                log.atInfo().log("Checklists retrieved successfully for profile: {}. Results: {}", 
                    profileUuid, checklists.size());

                return ResponseEntity.ok(checklists);

            } catch (Exception e) {
                Counter.builder("checklists.requests.error.total")
                    .tag("operation", "get_checklists")
                    .tag("error", e.getClass().getSimpleName())
                    .register(meterRegistry)
                    .increment();

                log.atError().withCause(e).log("Failed to get checklists for profile: {}", profileUuid);
                throw e;
            }
        });
    }

    /**
     * Get specific checklist by ID
     * GET /api/v1/analysis/profiles/{profileUuid}/checklists/{checklistId}
     */
    @GetMapping("/profiles/{profileUuid}/checklists/{checklistId}")
    public ResponseEntity<ChecklistResponseDto> getProfileChecklist(
            @PathVariable("profileUuid") @NotBlank String profileUuid,
            @PathVariable("checklistId") @NotBlank String checklistId) {

        log.atInfo().log("Checklist request for profile: {}, checklistId: {}", profileUuid, checklistId);

        try {
            ChecklistResponseDto checklist = gbgAnalysisService.getProfileChecklist(profileUuid, checklistId);

            log.atInfo().log("Checklist retrieved successfully for profile: {}, checklistId: {}", 
                profileUuid, checklistId);

            return ResponseEntity.ok(checklist);

        } catch (RuntimeException e) {
            if (e.getMessage().contains("not found")) {
                log.atWarn().log("Checklist not found for profile: {}, checklistId: {}", profileUuid, checklistId);
                return ResponseEntity.notFound().build();
            }
            
            log.atError().withCause(e).log("Failed to get checklist for profile: {}, checklistId: {}", 
                profileUuid, checklistId);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Refresh checklist data from GBG
     * POST /api/v1/analysis/profiles/{profileUuid}/checklists/refresh
     */
    @PostMapping("/profiles/{profileUuid}/checklists/refresh")
    public ResponseEntity<List<ChecklistResponseDto>> refreshProfileChecklists(
            @PathVariable("profileUuid") @NotBlank String profileUuid) {

        log.atInfo().log("Checklists refresh request for profile: {}", profileUuid);

        return analysisTimer.recordCallable(() -> {
            try {
                Counter.builder("checklists.refresh.total")
                    .tag("profile", profileUuid)
                    .register(meterRegistry)
                    .increment();

                List<ChecklistResponseDto> checklists = gbgAnalysisService.refreshProfileChecklists(profileUuid);

                Counter.builder("checklists.refresh.success.total")
                    .tag("profile", profileUuid)
                    .tag("results", String.valueOf(checklists.size()))
                    .register(meterRegistry)
                    .increment();

                log.atInfo().log("Checklists refreshed successfully for profile: {}. Results: {}", 
                    profileUuid, checklists.size());

                return ResponseEntity.ok(checklists);

            } catch (Exception e) {
                Counter.builder("checklists.refresh.error.total")
                    .tag("profile", profileUuid)
                    .tag("error", e.getClass().getSimpleName())
                    .register(meterRegistry)
                    .increment();

                log.atError().withCause(e).log("Failed to refresh checklists for profile: {}", profileUuid);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }
        });
    }

    /**
     * Check if profile is compliant
     * GET /api/v1/analysis/profiles/{profileUuid}/compliance/status
     */
    @GetMapping("/profiles/{profileUuid}/compliance/status")
    public ResponseEntity<Boolean> isProfileCompliant(
            @PathVariable("profileUuid") @NotBlank String profileUuid) {

        log.atInfo().log("Compliance status request for profile: {}", profileUuid);

        try {
            boolean isCompliant = gbgAnalysisService.isProfileCompliant(profileUuid);

            log.atInfo().log("Compliance status retrieved for profile: {}. Compliant: {}", 
                profileUuid, isCompliant);

            return ResponseEntity.ok(isCompliant);

        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to check compliance for profile: {}", profileUuid);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get compliance summary for a profile
     * GET /api/v1/analysis/profiles/{profileUuid}/compliance/summary
     */
    @GetMapping("/profiles/{profileUuid}/compliance/summary")
    public ResponseEntity<ChecklistResponseDto.ComplianceSummaryDto> getComplianceSummary(
            @PathVariable("profileUuid") @NotBlank String profileUuid) {

        log.atInfo().log("Compliance summary request for profile: {}", profileUuid);

        try {
            ChecklistResponseDto.ComplianceSummaryDto summary = gbgAnalysisService.getComplianceSummary(profileUuid);

            log.atInfo().log("Compliance summary retrieved for profile: {}. Status: {}, Score: {}", 
                profileUuid, summary.getComplianceStatus(), summary.getComplianceScore());

            return ResponseEntity.ok(summary);

        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to get compliance summary for profile: {}", profileUuid);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get analysis history for a profile
     * GET /api/v1/analysis/profiles/{profileUuid}/history
     */
    @GetMapping("/profiles/{profileUuid}/history")
    public ResponseEntity<List<AnalysisResponseDto>> getAnalysisHistory(
            @PathVariable("profileUuid") @NotBlank String profileUuid,
            @RequestParam(value = "limit", defaultValue = "10") 
            @Min(value = 1, message = "Limit must be at least 1")
            @Max(value = 100, message = "Limit cannot exceed 100") Integer limit) {

        log.atInfo().log("Analysis history request for profile: {}, limit: {}", profileUuid, limit);

        try {
            List<AnalysisResponseDto> history = gbgAnalysisService.getAnalysisHistory(profileUuid, limit);

            log.atInfo().log("Analysis history retrieved for profile: {}. Results: {}", 
                profileUuid, history.size());

            return ResponseEntity.ok(history);

        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to get analysis history for profile: {}", profileUuid);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Health check endpoint for analysis service
     * GET /api/v1/analysis/health
     */
    @GetMapping("/health")
    public ResponseEntity<String> healthCheck() {
        return ResponseEntity.ok("Analysis service is healthy");
    }
}
