package com.gumtree.tns.identityverification.service;

import com.gumtree.tns.identityverification.service.dto.WorkflowStateDto;
import com.gumtree.tns.identityverification.service.dto.WorkflowTransitionDto;

import java.util.List;

/**
 * Service for managing verification workflow states and transitions
 */
public interface VerificationWorkflowService {

    /**
     * Initialize workflow for a new verification request
     * 
     * @param profileUuid the GBG profile UUID
     * @param initiatedBy who initiated the workflow
     * @return initial workflow state
     */
    WorkflowStateDto initializeWorkflow(String profileUuid, String initiatedBy);

    /**
     * Get current workflow state
     * 
     * @param profileUuid the GBG profile UUID
     * @return current workflow state
     */
    WorkflowStateDto getCurrentWorkflowState(String profileUuid);

    /**
     * Transition workflow to next state
     * 
     * @param profileUuid the GBG profile UUID
     * @param targetState the target state
     * @param reason reason for transition
     * @param triggeredBy who triggered the transition
     * @param transitionData additional data for transition
     * @return updated workflow state
     */
    WorkflowStateDto transitionWorkflow(String profileUuid, WorkflowState targetState, 
                                       String reason, String triggeredBy, Object transitionData);

    /**
     * Check if workflow can progress to next state
     * 
     * @param profileUuid the GBG profile UUID
     * @param targetState the target state
     * @return true if transition is allowed
     */
    boolean canTransitionTo(String profileUuid, WorkflowState targetState);

    /**
     * Get workflow state history
     * 
     * @param profileUuid the GBG profile UUID
     * @return list of state transitions
     */
    List<WorkflowTransitionDto> getWorkflowHistory(String profileUuid);

    /**
     * Get next required actions for current state
     * 
     * @param profileUuid the GBG profile UUID
     * @return list of required actions
     */
    List<String> getNextRequiredActions(String profileUuid);

    /**
     * Mark workflow as blocked
     * 
     * @param profileUuid the GBG profile UUID
     * @param blockingReasons reasons for blocking
     * @param blockedBy who blocked the workflow
     */
    void blockWorkflow(String profileUuid, List<String> blockingReasons, String blockedBy);

    /**
     * Unblock workflow
     * 
     * @param profileUuid the GBG profile UUID
     * @param unblockedBy who unblocked the workflow
     */
    void unblockWorkflow(String profileUuid, String unblockedBy);

    /**
     * Check if workflow is blocked
     * 
     * @param profileUuid the GBG profile UUID
     * @return true if workflow is blocked
     */
    boolean isWorkflowBlocked(String profileUuid);

    /**
     * Get estimated completion date
     * 
     * @param profileUuid the GBG profile UUID
     * @return estimated completion date
     */
    java.time.LocalDateTime getEstimatedCompletionDate(String profileUuid);

    /**
     * Update estimated completion date
     * 
     * @param profileUuid the GBG profile UUID
     * @param estimatedDate new estimated completion date
     */
    void updateEstimatedCompletionDate(String profileUuid, java.time.LocalDateTime estimatedDate);

    /**
     * Workflow states enum
     */
    enum WorkflowState {
        INITIATED("INITIATED", "Verification process initiated"),
        COMPANY_SEARCH_COMPLETED("COMPANY_SEARCH_COMPLETED", "Company information search completed"),
        PROFILE_CREATED("PROFILE_CREATED", "GBG profile created"),
        REPRESENTATIVES_ADDED("REPRESENTATIVES_ADDED", "Company representatives added"),
        IDENTITY_VERIFICATION_IN_PROGRESS("IDENTITY_VERIFICATION_IN_PROGRESS", "Identity verification in progress"),
        IDENTITY_VERIFICATION_COMPLETED("IDENTITY_VERIFICATION_COMPLETED", "Identity verification completed"),
        RISK_ANALYSIS_IN_PROGRESS("RISK_ANALYSIS_IN_PROGRESS", "Risk analysis in progress"),
        RISK_ANALYSIS_COMPLETED("RISK_ANALYSIS_COMPLETED", "Risk analysis completed"),
        COMPLIANCE_CHECK_IN_PROGRESS("COMPLIANCE_CHECK_IN_PROGRESS", "Compliance check in progress"),
        COMPLIANCE_CHECK_COMPLETED("COMPLIANCE_CHECK_COMPLETED", "Compliance check completed"),
        MANUAL_REVIEW_REQUIRED("MANUAL_REVIEW_REQUIRED", "Manual review required"),
        APPROVED("APPROVED", "Verification approved"),
        REJECTED("REJECTED", "Verification rejected"),
        EXPIRED("EXPIRED", "Verification expired"),
        CANCELLED("CANCELLED", "Verification cancelled");

        private final String value;
        private final String description;

        WorkflowState(String value, String description) {
            this.value = value;
            this.description = description;
        }

        public String getValue() {
            return value;
        }

        public String getDescription() {
            return description;
        }

        public static WorkflowState fromValue(String value) {
            for (WorkflowState state : values()) {
                if (state.value.equals(value)) {
                    return state;
                }
            }
            throw new IllegalArgumentException("Unknown workflow state: " + value);
        }

        /**
         * Check if this state is a terminal state
         */
        public boolean isTerminal() {
            return this == APPROVED || this == REJECTED || this == EXPIRED || this == CANCELLED;
        }

        /**
         * Check if this state requires manual intervention
         */
        public boolean requiresManualIntervention() {
            return this == MANUAL_REVIEW_REQUIRED;
        }

        /**
         * Get valid next states from current state
         */
        public List<WorkflowState> getValidNextStates() {
            switch (this) {
                case INITIATED:
                    return List.of(COMPANY_SEARCH_COMPLETED, CANCELLED);
                case COMPANY_SEARCH_COMPLETED:
                    return List.of(PROFILE_CREATED, CANCELLED);
                case PROFILE_CREATED:
                    return List.of(REPRESENTATIVES_ADDED, IDENTITY_VERIFICATION_IN_PROGRESS, CANCELLED);
                case REPRESENTATIVES_ADDED:
                    return List.of(IDENTITY_VERIFICATION_IN_PROGRESS, CANCELLED);
                case IDENTITY_VERIFICATION_IN_PROGRESS:
                    return List.of(IDENTITY_VERIFICATION_COMPLETED, MANUAL_REVIEW_REQUIRED, CANCELLED);
                case IDENTITY_VERIFICATION_COMPLETED:
                    return List.of(RISK_ANALYSIS_IN_PROGRESS, MANUAL_REVIEW_REQUIRED, CANCELLED);
                case RISK_ANALYSIS_IN_PROGRESS:
                    return List.of(RISK_ANALYSIS_COMPLETED, MANUAL_REVIEW_REQUIRED, CANCELLED);
                case RISK_ANALYSIS_COMPLETED:
                    return List.of(COMPLIANCE_CHECK_IN_PROGRESS, MANUAL_REVIEW_REQUIRED, CANCELLED);
                case COMPLIANCE_CHECK_IN_PROGRESS:
                    return List.of(COMPLIANCE_CHECK_COMPLETED, MANUAL_REVIEW_REQUIRED, CANCELLED);
                case COMPLIANCE_CHECK_COMPLETED:
                    return List.of(APPROVED, REJECTED, MANUAL_REVIEW_REQUIRED);
                case MANUAL_REVIEW_REQUIRED:
                    return List.of(APPROVED, REJECTED, IDENTITY_VERIFICATION_IN_PROGRESS, 
                                 RISK_ANALYSIS_IN_PROGRESS, COMPLIANCE_CHECK_IN_PROGRESS);
                default:
                    return List.of(); // Terminal states have no valid next states
            }
        }
    }
}
