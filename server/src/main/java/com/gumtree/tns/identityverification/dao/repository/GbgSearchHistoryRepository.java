package com.gumtree.tns.identityverification.dao.repository;

import com.gumtree.tns.identityverification.dao.model.GbgSearchHistory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository for GBG search history operations
 */
@Repository
public interface GbgSearchHistoryRepository extends JpaRepository<GbgSearchHistory, UUID> {

    /**
     * Find search history by lookup ID
     */
    Optional<GbgSearchHistory> findByLookupId(Integer lookupId);

    /**
     * Find search history by user ID, ordered by creation date descending
     */
    @Query("SELECT s FROM GbgSearchHistory s WHERE s.createdByUserId = :userId ORDER BY s.createdAt DESC")
    List<GbgSearchHistory> findByCreatedByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * Find search history by account ID, ordered by creation date descending
     */
    @Query("SELECT s FROM GbgSearchHistory s WHERE s.createdByAccountId = :accountId ORDER BY s.createdAt DESC")
    List<GbgSearchHistory> findByCreatedByAccountIdOrderByCreatedAtDesc(@Param("accountId") Long accountId, @Param("limit") Integer limit);

    /**
     * Find recent searches by user ID
     */
    @Query(value = "SELECT * FROM gbg_search_history WHERE created_by_user_id = :userId ORDER BY created_at DESC LIMIT :limit", nativeQuery = true)
    List<GbgSearchHistory> findRecentSearchesByUserId(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * Find recent searches by account ID
     */
    @Query(value = "SELECT * FROM gbg_search_history WHERE created_by_account_id = :accountId ORDER BY created_at DESC LIMIT :limit", nativeQuery = true)
    List<GbgSearchHistory> findRecentSearchesByAccountId(@Param("accountId") Long accountId, @Param("limit") Integer limit);

    /**
     * Count searches by user ID in the last 24 hours
     */
    @Query("SELECT COUNT(s) FROM GbgSearchHistory s WHERE s.createdByUserId = :userId AND s.createdAt >= :since")
    Long countRecentSearchesByUserId(@Param("userId") Long userId, @Param("since") java.time.LocalDateTime since);

    /**
     * Count searches by account ID in the last 24 hours
     */
    @Query("SELECT COUNT(s) FROM GbgSearchHistory s WHERE s.createdByAccountId = :accountId AND s.createdAt >= :since")
    Long countRecentSearchesByAccountId(@Param("accountId") Long accountId, @Param("since") java.time.LocalDateTime since);

    /**
     * Find all searches with results count greater than specified value
     */
    List<GbgSearchHistory> findByResultCountGreaterThan(Integer minResults);

    /**
     * Find searches by search type
     */
    List<GbgSearchHistory> findBySearchTypeOrderByCreatedAtDesc(String searchType);
}
