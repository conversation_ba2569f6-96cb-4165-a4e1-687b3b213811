package com.gumtree.tns.identityverification.dao.repository;

import com.gumtree.tns.identityverification.dao.model.GbgChecklist;
import com.gumtree.tns.identityverification.dao.model.GbgProfile;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository for GBG Checklist operations
 */
@Repository
public interface GbgChecklistRepository extends JpaRepository<GbgChecklist, UUID> {

    /**
     * Find checklists by profile
     */
    List<GbgChecklist> findByGbgProfileOrderByCreatedAtDesc(GbgProfile gbgProfile);

    /**
     * Find checklists by profile UUID
     */
    @Query("SELECT c FROM GbgChecklist c WHERE c.gbgProfile.gbgProfileUuid = :profileUuid ORDER BY c.createdAt DESC")
    List<GbgChecklist> findByProfileUuidOrderByCreatedAtDesc(@Param("profileUuid") String profileUuid);

    /**
     * Find checklist by profile and GBG checklist ID
     */
    Optional<GbgChecklist> findByGbgProfileAndGbgChecklistId(GbgProfile gbgProfile, Integer gbgChecklistId);

    /**
     * Find checklist by profile UUID and GBG checklist ID
     */
    @Query("SELECT c FROM GbgChecklist c WHERE c.gbgProfile.gbgProfileUuid = :profileUuid AND c.gbgChecklistId = :checklistId")
    Optional<GbgChecklist> findByProfileUuidAndChecklistId(@Param("profileUuid") String profileUuid, @Param("checklistId") Integer checklistId);

    /**
     * Find checklists by name (case-insensitive)
     */
    @Query("SELECT c FROM GbgChecklist c WHERE LOWER(c.name) LIKE LOWER(CONCAT('%', :name, '%')) ORDER BY c.createdAt DESC")
    List<GbgChecklist> findByNameContainingIgnoreCase(@Param("name") String name);

    /**
     * Find completed checklists
     */
    @Query("SELECT c FROM GbgChecklist c WHERE c.uncompletedTasks = 0 AND c.completedTasks > 0 ORDER BY c.updatedAt DESC")
    List<GbgChecklist> findCompletedChecklists();

    /**
     * Find incomplete checklists
     */
    @Query("SELECT c FROM GbgChecklist c WHERE c.uncompletedTasks > 0 ORDER BY c.createdAt ASC")
    List<GbgChecklist> findIncompleteChecklists();

    /**
     * Find checklists by profile with completion status
     */
    @Query("SELECT c FROM GbgChecklist c WHERE c.gbgProfile = :profile AND " +
           "(:completed = true AND c.uncompletedTasks = 0 AND c.completedTasks > 0) OR " +
           "(:completed = false AND c.uncompletedTasks > 0) " +
           "ORDER BY c.updatedAt DESC")
    List<GbgChecklist> findByProfileAndCompletionStatus(@Param("profile") GbgProfile profile, @Param("completed") Boolean completed);

    /**
     * Find checklists with high completion percentage
     */
    @Query("SELECT c FROM GbgChecklist c WHERE " +
           "(c.completedTasks * 100.0 / (c.completedTasks + c.uncompletedTasks)) >= :minPercentage " +
           "ORDER BY (c.completedTasks * 100.0 / (c.completedTasks + c.uncompletedTasks)) DESC")
    List<GbgChecklist> findByCompletionPercentageGreaterThanEqual(@Param("minPercentage") Double minPercentage);

    /**
     * Find checklists created within date range
     */
    @Query("SELECT c FROM GbgChecklist c WHERE c.createdAt >= :startDate AND c.createdAt <= :endDate ORDER BY c.createdAt DESC")
    List<GbgChecklist> findByCreatedAtBetweenOrderByCreatedAtDesc(
        @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * Find checklists updated within date range
     */
    @Query("SELECT c FROM GbgChecklist c WHERE c.updatedAt >= :startDate AND c.updatedAt <= :endDate ORDER BY c.updatedAt DESC")
    List<GbgChecklist> findByUpdatedAtBetweenOrderByUpdatedAtDesc(
        @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * Count checklists by profile
     */
    Long countByGbgProfile(GbgProfile gbgProfile);

    /**
     * Count completed checklists by profile
     */
    @Query("SELECT COUNT(c) FROM GbgChecklist c WHERE c.gbgProfile = :profile AND c.uncompletedTasks = 0 AND c.completedTasks > 0")
    Long countCompletedByProfile(@Param("profile") GbgProfile profile);

    /**
     * Count incomplete checklists by profile
     */
    @Query("SELECT COUNT(c) FROM GbgChecklist c WHERE c.gbgProfile = :profile AND c.uncompletedTasks > 0")
    Long countIncompleteByProfile(@Param("profile") GbgProfile profile);

    /**
     * Get total tasks count by profile
     */
    @Query("SELECT SUM(c.completedTasks + c.uncompletedTasks) FROM GbgChecklist c WHERE c.gbgProfile = :profile")
    Long getTotalTasksByProfile(@Param("profile") GbgProfile profile);

    /**
     * Get completed tasks count by profile
     */
    @Query("SELECT SUM(c.completedTasks) FROM GbgChecklist c WHERE c.gbgProfile = :profile")
    Long getCompletedTasksByProfile(@Param("profile") GbgProfile profile);

    /**
     * Get uncompleted tasks count by profile
     */
    @Query("SELECT SUM(c.uncompletedTasks) FROM GbgChecklist c WHERE c.gbgProfile = :profile")
    Long getUncompletedTasksByProfile(@Param("profile") GbgProfile profile);

    /**
     * Find checklists that need attention (old incomplete checklists)
     */
    @Query("SELECT c FROM GbgChecklist c WHERE c.uncompletedTasks > 0 AND c.createdAt < :cutoffTime ORDER BY c.createdAt ASC")
    List<GbgChecklist> findChecklistsNeedingAttention(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * Get average completion percentage by profile
     */
    @Query("SELECT AVG(c.completedTasks * 100.0 / (c.completedTasks + c.uncompletedTasks)) FROM GbgChecklist c WHERE " +
           "c.gbgProfile = :profile AND (c.completedTasks + c.uncompletedTasks) > 0")
    Double getAverageCompletionPercentageByProfile(@Param("profile") GbgProfile profile);

    /**
     * Find profiles with all checklists completed
     */
    @Query("SELECT DISTINCT c.gbgProfile FROM GbgChecklist c WHERE c.gbgProfile NOT IN " +
           "(SELECT c2.gbgProfile FROM GbgChecklist c2 WHERE c2.uncompletedTasks > 0)")
    List<GbgProfile> findProfilesWithAllChecklistsCompleted();

    /**
     * Check if profile has any completed checklists
     */
    @Query("SELECT CASE WHEN COUNT(c) > 0 THEN true ELSE false END FROM GbgChecklist c WHERE " +
           "c.gbgProfile = :profile AND c.uncompletedTasks = 0 AND c.completedTasks > 0")
    boolean hasCompletedChecklists(@Param("profile") GbgProfile profile);

    /**
     * Find most recently updated checklist by profile
     */
    @Query("SELECT c FROM GbgChecklist c WHERE c.gbgProfile = :profile ORDER BY c.updatedAt DESC")
    Optional<GbgChecklist> findMostRecentlyUpdatedByProfile(@Param("profile") GbgProfile profile);

    /**
     * Delete old checklists (cleanup)
     */
    @Query("DELETE FROM GbgChecklist c WHERE c.createdAt < :cutoffDate")
    void deleteOldChecklists(@Param("cutoffDate") LocalDateTime cutoffDate);
}
