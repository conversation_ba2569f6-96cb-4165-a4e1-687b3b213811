package com.gumtree.tns.identityverification.dao.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Entity representing GBG Analysis Results
 */
@Entity
@Table(name = "gbg_analysis_results")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GbgAnalysisResult {

    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", updatable = false, nullable = false)
    private UUID id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "gbg_profile_id", nullable = false)
    private GbgProfile gbgProfile;

    @Column(name = "analysis_id")
    private String analysisId;

    @Column(name = "score")
    private Integer score;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private AnalysisStatus status;

    @Enumerated(EnumType.STRING)
    @Column(name = "risk_level")
    private RiskLevel riskLevel;

    @Type(type = "jsonb")
    @Column(name = "analysis_report", columnDefinition = "jsonb")
    private String analysisReport;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        if (createdAt == null) {
            createdAt = now;
        }
        if (updatedAt == null) {
            updatedAt = now;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    /**
     * Analysis status enum
     */
    public enum AnalysisStatus {
        FAILED("failed"),
        COMPLETED("completed"),
        PENDING("pending"),
        MISSING_DATA("missing_data");

        private final String value;

        AnalysisStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static AnalysisStatus fromValue(String value) {
            for (AnalysisStatus status : values()) {
                if (status.value.equals(value)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("Unknown analysis status: " + value);
        }
    }

    /**
     * Risk level enum
     */
    public enum RiskLevel {
        LOW("Low"),
        MEDIUM("Medium"),
        HIGH("High");

        private final String displayName;

        RiskLevel(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }

        public static RiskLevel fromDisplayName(String displayName) {
            for (RiskLevel level : values()) {
                if (level.displayName.equals(displayName)) {
                    return level;
                }
            }
            throw new IllegalArgumentException("Unknown risk level: " + displayName);
        }
    }
}
