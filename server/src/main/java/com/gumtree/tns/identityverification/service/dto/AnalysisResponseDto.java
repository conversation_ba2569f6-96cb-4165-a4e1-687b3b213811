package com.gumtree.tns.identityverification.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DTO for analysis response data
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AnalysisResponseDto {

    private String profileId;
    private String analysisId;
    private Integer score;
    private String status; // failed, completed, pending, missing_data
    private String riskLevel; // Low, Medium, High
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private List<AnalysisReportDto> reports;
    private RiskSummaryDto riskSummary;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AnalysisReportDto {
        private String name;
        private String description;
        private List<AnalysisResultDto> results;
        private String overallResult;
        private Integer overallScore;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AnalysisResultDto {
        private String id;
        private String label;
        private String description;
        private Integer score;
        private String state; // skipped, analysed, failed
        private String result; // pass, fail, warning, info
        private String details;
        private List<String> recommendations;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskSummaryDto {
        private Integer totalScore;
        private String riskCategory; // Low, Medium, High
        private String riskLevel;
        private List<RiskFactorDto> riskFactors;
        private List<String> recommendations;
        private Boolean requiresManualReview;
        private String reviewReason;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RiskFactorDto {
        private String category;
        private String factor;
        private Integer impact; // 1-10 scale
        private String description;
        private String mitigation;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AnalysisMetricsDto {
        private Integer totalChecks;
        private Integer passedChecks;
        private Integer failedChecks;
        private Integer warningChecks;
        private Integer skippedChecks;
        private Double completionPercentage;
        private LocalDateTime lastAnalysisDate;
    }
}
