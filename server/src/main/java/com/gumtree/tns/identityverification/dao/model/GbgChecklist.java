package com.gumtree.tns.identityverification.dao.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Entity representing GBG Checklists
 */
@Entity
@Table(name = "gbg_checklists")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GbgChecklist {

    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", updatable = false, nullable = false)
    private UUID id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "gbg_profile_id", nullable = false)
    private GbgProfile gbgProfile;

    @Column(name = "gbg_checklist_id", nullable = false)
    private Integer gbgChecklistId;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "uncompleted_tasks", nullable = false)
    @Builder.Default
    private Integer uncompletedTasks = 0;

    @Column(name = "completed_tasks", nullable = false)
    @Builder.Default
    private Integer completedTasks = 0;

    @Type(type = "jsonb")
    @Column(name = "sections_data", columnDefinition = "jsonb")
    private String sectionsData;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        if (createdAt == null) {
            createdAt = now;
        }
        if (updatedAt == null) {
            updatedAt = now;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    /**
     * Calculate total tasks
     */
    public Integer getTotalTasks() {
        return (completedTasks != null ? completedTasks : 0) + 
               (uncompletedTasks != null ? uncompletedTasks : 0);
    }

    /**
     * Calculate completion percentage
     */
    public Double getCompletionPercentage() {
        Integer total = getTotalTasks();
        if (total == 0) {
            return 0.0;
        }
        return (completedTasks != null ? completedTasks : 0) * 100.0 / total;
    }

    /**
     * Check if checklist is completed
     */
    public Boolean isCompleted() {
        return uncompletedTasks != null && uncompletedTasks == 0 && 
               completedTasks != null && completedTasks > 0;
    }

    /**
     * Get overall status based on completion
     */
    public String getOverallStatus() {
        if (isCompleted()) {
            return "passed";
        } else if (completedTasks != null && completedTasks > 0) {
            return "needs_review";
        } else {
            return "failed";
        }
    }
}
