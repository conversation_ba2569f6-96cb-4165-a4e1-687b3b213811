package com.gumtree.tns.identityverification.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DTO for company search responses
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchResponseDto {

    private Integer lookupId;
    private List<SearchResultDto> results;
    private Integer totalResults;
    private LocalDateTime searchedAt;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SearchResultDto {
        private Integer responseId;
        private String type;
        private String name;
        private String companyReference;
        private String provider;
        private String providerReference;
        private String jurisdictionCode;
        private String companyType;
        private String currentStatus;
        private AddressDto address;
        private Double matchScore; // Calculated match score
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AddressDto {
        private String line1;
        private String line2;
        private String countryCode;
        private String town;
        private String city;
        private String postCode;
        private String province;
        private String state;
        private String fullAddress;
    }
}
