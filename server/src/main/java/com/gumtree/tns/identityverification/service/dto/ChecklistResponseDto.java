package com.gumtree.tns.identityverification.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DTO for checklist response data
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChecklistResponseDto {

    private String profileId;
    private Integer checklistId;
    private String name;
    private String description;
    private Integer uncompletedTasks;
    private Integer completedTasks;
    private Integer totalTasks;
    private Double completionPercentage;
    private String overallStatus; // exempt, failed, passed, needs_review
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private List<ChecklistSectionDto> sections;
    private ComplianceSummaryDto complianceSummary;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChecklistSectionDto {
        private Integer sectionId;
        private String name;
        private String description;
        private String status; // exempt, failed, passed, needs_review
        private Integer totalComments;
        private Boolean memberBelongsToTeam;
        private List<ChecklistTaskDto> tasks;
        private List<ChecklistCommentDto> comments;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChecklistTaskDto {
        private Integer taskId;
        private String name;
        private String description;
        private String status; // pending, completed, failed, skipped
        private String assignedTeam;
        private Integer teamId;
        private String assignedUser;
        private LocalDateTime dueDate;
        private LocalDateTime completedAt;
        private String completionNotes;
        private List<String> requiredDocuments;
        private List<String> attachedDocuments;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChecklistCommentDto {
        private Integer commentId;
        private String content;
        private String authorName;
        private String authorRole;
        private LocalDateTime createdAt;
        private String commentType; // note, question, approval, rejection
        private Boolean isInternal;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ComplianceSummaryDto {
        private Boolean isCompliant;
        private String complianceStatus; // compliant, non_compliant, pending_review, incomplete
        private List<String> missingRequirements;
        private List<String> failedChecks;
        private List<String> pendingActions;
        private Integer complianceScore; // 0-100
        private LocalDateTime lastReviewDate;
        private String reviewerName;
        private String nextReviewDate;
        private List<ComplianceIssueDto> issues;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ComplianceIssueDto {
        private String issueType; // critical, major, minor, warning
        private String category;
        private String description;
        private String resolution;
        private String status; // open, in_progress, resolved, deferred
        private LocalDateTime identifiedAt;
        private LocalDateTime dueDate;
        private String assignedTo;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChecklistMetricsDto {
        private Integer totalChecklists;
        private Integer completedChecklists;
        private Integer pendingChecklists;
        private Integer failedChecklists;
        private Double averageCompletionTime; // in hours
        private LocalDateTime oldestPendingDate;
        private List<String> frequentIssues;
    }
}
