package com.gumtree.tns.identityverification.service.mapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gumtree.tns.identityverification.dao.model.GbgSearchHistory;
import com.gumtree.tns.identityverification.gateway.client.dto.GbgApiClientDtos.*;
import com.gumtree.tns.identityverification.service.dto.SearchRequestDto;
import com.gumtree.tns.identityverification.service.dto.SearchResponseDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for GBG search operations
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GbgSearchMapper {

    private final ObjectMapper objectMapper;

    /**
     * Convert SearchRequestDto to GbgSearchRequest
     */
    public GbgSearchRequest toGbgSearchRequest(SearchRequestDto dto) {
        return GbgSearchRequest.builder()
            .type(dto.getType())
            .name(dto.getName())
            .vatNumber(dto.getVatNumber())
            .companyReference(dto.getCompanyReference())
            .address(toGbgAddress(dto.getAddress()))
            .build();
    }

    /**
     * Convert AddressDto to GbgAddress
     */
    public GbgAddress toGbgAddress(SearchRequestDto.AddressDto dto) {
        if (dto == null) {
            return null;
        }
        
        return GbgAddress.builder()
            .line1(dto.getLine1())
            .line2(dto.getLine2())
            .countryCode(dto.getCountryCode())
            .town(dto.getTown())
            .city(dto.getCity())
            .postCode(dto.getPostCode())
            .province(dto.getProvince())
            .state(dto.getState())
            .build();
    }

    /**
     * Convert GbgSearchResponse to SearchResponseDto
     */
    public SearchResponseDto toSearchResponseDto(GbgSearchResponse gbgResponse) {
        if (gbgResponse == null) {
            return SearchResponseDto.builder()
                .totalResults(0)
                .build();
        }

        List<SearchResponseDto.SearchResultDto> results = gbgResponse.getData() != null 
            ? gbgResponse.getData().stream()
                .map(this::toSearchResultDto)
                .collect(Collectors.toList())
            : List.of();

        return SearchResponseDto.builder()
            .lookupId(gbgResponse.getLookupId())
            .results(results)
            .totalResults(results.size())
            .build();
    }

    /**
     * Convert GbgSearchResult to SearchResultDto
     */
    public SearchResponseDto.SearchResultDto toSearchResultDto(GbgSearchResult gbgResult) {
        return SearchResponseDto.SearchResultDto.builder()
            .responseId(gbgResult.getResponseId())
            .type(gbgResult.getType())
            .name(gbgResult.getName())
            .companyReference(gbgResult.getCompanyReference())
            .provider(gbgResult.getProvider())
            .providerReference(gbgResult.getProviderReference())
            .jurisdictionCode(gbgResult.getJurisdictionCode())
            .companyType(gbgResult.getCompanyType())
            .currentStatus(gbgResult.getCurrentStatus())
            .address(toAddressDto(gbgResult.getAddress()))
            .matchScore(calculateMatchScore(gbgResult))
            .build();
    }

    /**
     * Convert GbgAddress to AddressDto
     */
    public SearchResponseDto.AddressDto toAddressDto(GbgAddress gbgAddress) {
        if (gbgAddress == null) {
            return null;
        }

        return SearchResponseDto.AddressDto.builder()
            .line1(gbgAddress.getLine1())
            .line2(gbgAddress.getLine2())
            .countryCode(gbgAddress.getCountryCode())
            .town(gbgAddress.getTown())
            .city(gbgAddress.getCity())
            .postCode(gbgAddress.getPostCode())
            .province(gbgAddress.getProvince())
            .state(gbgAddress.getState())
            .fullAddress(buildFullAddress(gbgAddress))
            .build();
    }

    /**
     * Convert object to JSON string
     */
    public String toJsonString(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.atError().withCause(e).log("Failed to convert object to JSON string");
            return "{}";
        }
    }

    /**
     * Parse JSON string to object
     */
    public <T> T fromJsonString(String json, Class<T> clazz) {
        try {
            return objectMapper.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            log.atError().withCause(e).log("Failed to parse JSON string to object");
            return null;
        }
    }

    /**
     * Convert search history list to response DTO
     */
    public SearchResponseDto toSearchHistoryResponseDto(List<GbgSearchHistory> searchHistory) {
        List<SearchResponseDto.SearchResultDto> allResults = searchHistory.stream()
            .flatMap(history -> {
                try {
                    GbgSearchResponse gbgResponse = fromJsonString(history.getSearchResults(), GbgSearchResponse.class);
                    if (gbgResponse != null && gbgResponse.getData() != null) {
                        return gbgResponse.getData().stream().map(this::toSearchResultDto);
                    }
                } catch (Exception e) {
                    log.atWarn().withCause(e).log("Failed to parse search results from history");
                }
                return java.util.stream.Stream.empty();
            })
            .collect(Collectors.toList());

        return SearchResponseDto.builder()
            .results(allResults)
            .totalResults(allResults.size())
            .build();
    }

    /**
     * Parse search results from search history
     */
    public SearchResponseDto parseSearchResults(GbgSearchHistory searchHistory) {
        try {
            GbgSearchResponse gbgResponse = fromJsonString(searchHistory.getSearchResults(), GbgSearchResponse.class);
            SearchResponseDto response = toSearchResponseDto(gbgResponse);
            response.setSearchedAt(searchHistory.getCreatedAt());
            return response;
        } catch (Exception e) {
            log.atError().withCause(e).log("Failed to parse search results from history");
            return SearchResponseDto.builder()
                .lookupId(searchHistory.getLookupId())
                .totalResults(0)
                .searchedAt(searchHistory.getCreatedAt())
                .build();
        }
    }

    /**
     * Calculate match score for search result
     * This is a simplified implementation - in production, you might want more sophisticated matching
     */
    private Double calculateMatchScore(GbgSearchResult result) {
        // Simple scoring based on available data completeness
        double score = 0.0;
        
        if (result.getName() != null && !result.getName().trim().isEmpty()) {
            score += 30.0;
        }
        
        if (result.getCompanyReference() != null && !result.getCompanyReference().trim().isEmpty()) {
            score += 25.0;
        }
        
        if (result.getAddress() != null) {
            score += 20.0;
            if (result.getAddress().getPostCode() != null) {
                score += 15.0;
            }
        }
        
        if (result.getCurrentStatus() != null && result.getCurrentStatus().equalsIgnoreCase("active")) {
            score += 10.0;
        }
        
        return Math.min(score, 100.0);
    }

    /**
     * Build full address string from GbgAddress
     */
    private String buildFullAddress(GbgAddress address) {
        StringBuilder fullAddress = new StringBuilder();
        
        if (address.getLine1() != null) {
            fullAddress.append(address.getLine1());
        }
        
        if (address.getLine2() != null) {
            if (fullAddress.length() > 0) fullAddress.append(", ");
            fullAddress.append(address.getLine2());
        }
        
        if (address.getCity() != null) {
            if (fullAddress.length() > 0) fullAddress.append(", ");
            fullAddress.append(address.getCity());
        }
        
        if (address.getState() != null) {
            if (fullAddress.length() > 0) fullAddress.append(", ");
            fullAddress.append(address.getState());
        }
        
        if (address.getPostCode() != null) {
            if (fullAddress.length() > 0) fullAddress.append(" ");
            fullAddress.append(address.getPostCode());
        }
        
        if (address.getCountryCode() != null) {
            if (fullAddress.length() > 0) fullAddress.append(", ");
            fullAddress.append(address.getCountryCode());
        }
        
        return fullAddress.toString();
    }
}
