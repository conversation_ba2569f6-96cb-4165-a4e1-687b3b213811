package com.gumtree.tns.identityverification.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

/**
 * DTO for representative creation/update requests
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RepresentativeRequestDto {

    @Valid
    @NotNull(message = "Name is required")
    private NameDto name;

    @Email(message = "Invalid email format")
    private String email;

    @NotEmpty(message = "At least one representative type is required")
    private List<String> types; // director, identity, ownership, declaratory, principal, director_company

    private String dateOfBirth; // yyyy-mm-dd format

    private String professionalProfile;

    private String jobTitle;

    private String residenceCountryCode;

    private String birthCountryCode;

    private String nationality;

    @Valid
    private AddressDto address;

    private String personalNumber;

    private String customerReference;

    private PersonTypeDetailsDto personTypeDetails;

    private Boolean copyAddress;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NameDto {
        @NotBlank(message = "First name is required")
        private String firstName;
        
        private String middleName;
        
        @NotBlank(message = "Last name is required")
        private String lastName;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AddressDto {
        private String apartmentNumber;
        
        @NotBlank(message = "Address line 1 is required")
        private String address1;
        
        private String address2;
        
        @NotBlank(message = "City is required")
        private String city;
        
        private String state;
        
        private String postCode;
        
        @NotBlank(message = "Country code is required")
        private String countryCode;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PersonTypeDetailsDto {
        private String appointmentDate; // yyyy-mm-dd format
        private Boolean majority;
        private Boolean governing;
        private Boolean responsibility;
        private String majorityPercentage;
        private String specificOwnershipPercentage;
    }
}
