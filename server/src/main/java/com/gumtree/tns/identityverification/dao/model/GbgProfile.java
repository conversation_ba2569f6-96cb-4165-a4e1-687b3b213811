package com.gumtree.tns.identityverification.dao.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Entity representing GBG Profile - central entity for all GBG operations
 */
@Entity
@Table(name = "gbg_profiles")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GbgProfile {

    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", updatable = false, nullable = false)
    private UUID id;

    @Column(name = "gbg_profile_uuid", nullable = false, unique = true)
    private String gbgProfileUuid;

    @Column(name = "customer_reference", nullable = false, unique = true)
    private String customerReference;

    @Enumerated(EnumType.STRING)
    @Column(name = "profile_type", nullable = false)
    private ProfileType profileType;

    @Column(name = "company_name")
    private String companyName;

    @Column(name = "company_number")
    private String companyNumber;

    @Column(name = "vat_number")
    private String vatNumber;

    @Column(name = "domain")
    private String domain;

    @Enumerated(EnumType.STRING)
    @Column(name = "review_status", nullable = false)
    private ReviewStatus reviewStatus;

    @Column(name = "risk_id")
    private String riskId;

    @Column(name = "risk_label")
    private String riskLabel;

    @Enumerated(EnumType.STRING)
    @Column(name = "risk_category")
    private RiskCategory riskCategory;

    @Column(name = "team_id")
    private Integer teamId;

    @Column(name = "team_label")
    private String teamLabel;

    @Column(name = "assignee_id")
    private String assigneeId;

    @Column(name = "assignee_name")
    private String assigneeName;

    @Column(name = "configuration_id")
    private Integer configurationId;

    @Column(name = "configuration_name")
    private String configurationName;

    @Type(type = "jsonb")
    @Column(name = "address_data", columnDefinition = "jsonb")
    private String addressData;

    @Type(type = "jsonb")
    @Column(name = "company_data", columnDefinition = "jsonb")
    private String companyData;

    @Type(type = "jsonb")
    @Column(name = "onboarding_statuses", columnDefinition = "jsonb")
    private String onboardingStatuses;

    @Column(name = "review_at")
    private LocalDateTime reviewAt;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // Relationships
    @OneToMany(mappedBy = "gbgProfile", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<GbgRepresentative> representatives;

    @OneToMany(mappedBy = "gbgProfile", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<IdentityVerificationRequest> verificationRequests;

    @PrePersist
    protected void onCreate() {
        LocalDateTime now = LocalDateTime.now();
        if (createdAt == null) {
            createdAt = now;
        }
        if (updatedAt == null) {
            updatedAt = now;
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    public enum ProfileType {
        KYC, KYB
    }

    public enum ReviewStatus {
        PROCESSED("Processed"),
        NEEDS_REVIEW("Needs Review"),
        ADDITIONAL_INFO_REQUIRED("Additional Info Required"),
        APPROVED("Approved"),
        DECLINED("Declined"),
        ARCHIVED("Archived");

        private final String displayName;

        ReviewStatus(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    public enum RiskCategory {
        LOW("Low"),
        MEDIUM("Medium"),
        HIGH("High");

        private final String displayName;

        RiskCategory(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }
}
