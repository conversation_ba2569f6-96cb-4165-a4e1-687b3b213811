package com.gumtree.tns.identityverification.service.mapper;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.gumtree.tns.identityverification.dao.model.GbgPortalUser;
import com.gumtree.tns.identityverification.dao.model.GbgPortalUserActivity;
import com.gumtree.tns.identityverification.dao.model.GbgProfile;
import com.gumtree.tns.identityverification.gateway.client.dto.GbgApiClientDtos.*;
import com.gumtree.tns.identityverification.service.dto.PortalUserActivityDto;
import com.gumtree.tns.identityverification.service.dto.PortalUserRequestDto;
import com.gumtree.tns.identityverification.service.dto.PortalUserResponseDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper for GBG Portal User operations
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GbgPortalUserMapper {

    private final ObjectMapper objectMapper;

    /**
     * Convert PortalUserRequestDto to GbgCreatePortalUserRequest
     */
    public GbgCreatePortalUserRequest toGbgCreateRequest(PortalUserRequestDto dto) {
        return GbgCreatePortalUserRequest.builder()
            .email(dto.getEmail())
            .name(dto.getName())
            .role(dto.getRole() != null ? dto.getRole() : "admin")
            .profileId(dto.getProfileId())
            .language(dto.getLanguage())
            .timezone(dto.getTimezone())
            .emailNotifications(dto.getPreferences() != null ? dto.getPreferences().getEmailNotifications() : true)
            .smsNotifications(dto.getPreferences() != null ? dto.getPreferences().getSmsNotifications() : false)
            .build();
    }

    /**
     * Convert PortalUserRequestDto to GbgUpdatePortalUserRequest
     */
    public GbgUpdatePortalUserRequest toGbgUpdateRequest(PortalUserRequestDto dto, Integer gbgUserId) {
        return GbgUpdatePortalUserRequest.builder()
            .userId(gbgUserId)
            .email(dto.getEmail())
            .name(dto.getName())
            .role(dto.getRole())
            .language(dto.getLanguage())
            .timezone(dto.getTimezone())
            .emailNotifications(dto.getPreferences() != null ? dto.getPreferences().getEmailNotifications() : null)
            .smsNotifications(dto.getPreferences() != null ? dto.getPreferences().getSmsNotifications() : null)
            .build();
    }

    /**
     * Convert GbgPortalUserResponse to GbgPortalUser entity
     */
    public GbgPortalUser toEntity(GbgPortalUserResponse gbgResponse, GbgProfile profile) {
        return GbgPortalUser.builder()
            .gbgUserId(gbgResponse.getId())
            .gbgProfile(profile)
            .email(gbgResponse.getEmail())
            .name(gbgResponse.getName())
            .role(mapRole(gbgResponse.getRole()))
            .status(GbgPortalUser.PortalUserStatus.ACTIVE)
            .language(gbgResponse.getLanguage() != null ? gbgResponse.getLanguage() : "en")
            .timezone(gbgResponse.getTimezone() != null ? gbgResponse.getTimezone() : "UTC")
            .emailNotifications(gbgResponse.getEmailNotifications() != null ? gbgResponse.getEmailNotifications() : true)
            .smsNotifications(gbgResponse.getSmsNotifications() != null ? gbgResponse.getSmsNotifications() : false)
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .build();
    }

    /**
     * Convert GbgPortalUser entity to PortalUserResponseDto
     */
    public PortalUserResponseDto toResponseDto(GbgPortalUser entity) {
        return PortalUserResponseDto.builder()
            .id(entity.getGbgUserId())
            .email(entity.getEmail())
            .name(entity.getName())
            .profileId(entity.getGbgProfile() != null ? entity.getGbgProfile().getGbgProfileUuid() : null)
            .role(entity.getRole().getValue())
            .status(entity.getStatus().getValue())
            .createdAt(entity.getCreatedAt())
            .updatedAt(entity.getUpdatedAt())
            .lastLoginAt(entity.getLastLoginAt())
            .language(entity.getLanguage())
            .timezone(entity.getTimezone())
            .preferences(buildPreferencesDto(entity))
            .recentActivity(buildRecentActivitySummary(entity))
            .build();
    }

    /**
     * Convert GbgPortalUserActivity entity to PortalUserActivityDto
     */
    public PortalUserActivityDto toActivityDto(GbgPortalUserActivity entity) {
        return PortalUserActivityDto.builder()
            .id(entity.getId().toString())
            .userId(entity.getGbgPortalUser().getGbgUserId().toString())
            .profileId(entity.getGbgProfile() != null ? entity.getGbgProfile().getGbgProfileUuid() : null)
            .email(entity.getGbgPortalUser().getEmail())
            .ipAddress(entity.getIpAddress())
            .userAgent(entity.getUserAgent())
            .activityType(entity.getActivityType().getValue())
            .activityName(entity.getActivityName())
            .description(entity.getDescription())
            .activityDetails(parseActivityDetails(entity.getActivityDetails()))
            .createdAt(entity.getCreatedAt())
            .updatedAt(entity.getUpdatedAt())
            .sessionId(entity.getSessionId())
            .deviceType(entity.getDeviceType())
            .browserName(entity.getBrowserName())
            .operatingSystem(entity.getOperatingSystem())
            .location(entity.getLocation())
            .isSuccessful(entity.getIsSuccessful())
            .errorMessage(entity.getErrorMessage())
            .durationMs(entity.getDurationMs())
            .build();
    }

    /**
     * Update entity with request data
     */
    public void updateEntity(GbgPortalUser entity, PortalUserRequestDto request) {
        if (request.getName() != null) {
            entity.setName(request.getName());
        }
        if (request.getRole() != null) {
            entity.setRole(mapRole(request.getRole()));
        }
        if (request.getLanguage() != null) {
            entity.setLanguage(request.getLanguage());
        }
        if (request.getTimezone() != null) {
            entity.setTimezone(request.getTimezone());
        }
        if (request.getPreferences() != null) {
            if (request.getPreferences().getEmailNotifications() != null) {
                entity.setEmailNotifications(request.getPreferences().getEmailNotifications());
            }
            if (request.getPreferences().getSmsNotifications() != null) {
                entity.setSmsNotifications(request.getPreferences().getSmsNotifications());
            }
            if (request.getPreferences().getNotificationFrequency() != null) {
                entity.setNotificationFrequency(request.getPreferences().getNotificationFrequency());
            }
        }
        entity.setUpdatedAt(LocalDateTime.now());
    }

    // Helper methods

    private GbgPortalUser.PortalUserRole mapRole(String role) {
        try {
            return GbgPortalUser.PortalUserRole.fromValue(role != null ? role : "admin");
        } catch (Exception e) {
            log.atWarn().log("Unknown portal user role: {}, defaulting to ADMIN", role);
            return GbgPortalUser.PortalUserRole.ADMIN;
        }
    }

    private PortalUserResponseDto.PortalUserPreferencesDto buildPreferencesDto(GbgPortalUser entity) {
        return PortalUserResponseDto.PortalUserPreferencesDto.builder()
            .emailNotifications(entity.getEmailNotifications())
            .smsNotifications(entity.getSmsNotifications())
            .notificationFrequency(entity.getNotificationFrequency())
            .build();
    }

    private List<PortalUserResponseDto.PortalUserActivitySummaryDto> buildRecentActivitySummary(GbgPortalUser entity) {
        if (entity.getActivities() == null || entity.getActivities().isEmpty()) {
            return List.of();
        }

        return entity.getActivities().stream()
            .limit(5) // Show only last 5 activities
            .map(activity -> PortalUserResponseDto.PortalUserActivitySummaryDto.builder()
                .activityType(activity.getActivityType().getValue())
                .description(activity.getDescription())
                .timestamp(activity.getCreatedAt())
                .ipAddress(activity.getIpAddress())
                .userAgent(activity.getUserAgent())
                .build())
            .collect(Collectors.toList());
    }

    private java.util.Map<String, Object> parseActivityDetails(String activityDetails) {
        if (activityDetails == null || activityDetails.trim().isEmpty()) {
            return java.util.Map.of();
        }

        try {
            return objectMapper.readValue(activityDetails, 
                objectMapper.getTypeFactory().constructMapType(java.util.Map.class, String.class, Object.class));
        } catch (JsonProcessingException e) {
            log.atWarn().withCause(e).log("Failed to parse activity details JSON");
            return java.util.Map.of();
        }
    }

    private String toJsonString(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.atError().withCause(e).log("Failed to convert object to JSON string");
            return "{}";
        }
    }
}
