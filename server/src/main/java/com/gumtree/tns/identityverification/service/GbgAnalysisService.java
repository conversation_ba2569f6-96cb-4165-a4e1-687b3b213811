package com.gumtree.tns.identityverification.service;

import com.gumtree.tns.identityverification.service.dto.AnalysisResponseDto;
import com.gumtree.tns.identityverification.service.dto.ChecklistResponseDto;

import java.util.List;

/**
 * Service for handling GBG analysis and compliance operations
 */
public interface GbgAnalysisService {

    /**
     * Get risk analysis for a profile
     * 
     * @param profileUuid the GBG profile UUID
     * @return analysis results
     */
    List<AnalysisResponseDto> getProfileAnalysis(String profileUuid);

    /**
     * Refresh analysis data from GBG
     * 
     * @param profileUuid the GBG profile UUID
     * @return updated analysis results
     */
    List<AnalysisResponseDto> refreshProfileAnalysis(String profileUuid);

    /**
     * Get compliance checklists for a profile
     * 
     * @param profileUuid the GBG profile UUID
     * @return checklist results
     */
    List<ChecklistResponseDto> getProfileChecklists(String profileUuid);

    /**
     * Get specific checklist by ID
     * 
     * @param profileUuid the GBG profile UUID
     * @param checklistId the checklist ID
     * @return checklist details
     */
    ChecklistResponseDto getProfileChecklist(String profileUuid, String checklistId);

    /**
     * Refresh checklist data from GBG
     * 
     * @param profileUuid the GBG profile UUID
     * @return updated checklist results
     */
    List<ChecklistResponseDto> refreshProfileChecklists(String profileUuid);

    /**
     * Get overall risk score for a profile
     * 
     * @param profileUuid the GBG profile UUID
     * @return risk score and category
     */
    AnalysisResponseDto.RiskSummaryDto getProfileRiskSummary(String profileUuid);

    /**
     * Check if profile meets compliance requirements
     * 
     * @param profileUuid the GBG profile UUID
     * @return compliance status
     */
    boolean isProfileCompliant(String profileUuid);

    /**
     * Get compliance summary for a profile
     * 
     * @param profileUuid the GBG profile UUID
     * @return compliance summary
     */
    ChecklistResponseDto.ComplianceSummaryDto getComplianceSummary(String profileUuid);

    /**
     * Get analysis history for a profile
     * 
     * @param profileUuid the GBG profile UUID
     * @param limit maximum number of results
     * @return historical analysis results
     */
    List<AnalysisResponseDto> getAnalysisHistory(String profileUuid, Integer limit);
}
