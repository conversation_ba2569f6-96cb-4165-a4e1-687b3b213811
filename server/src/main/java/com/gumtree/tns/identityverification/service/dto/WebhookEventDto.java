package com.gumtree.tns.identityverification.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * DTO for webhook event information
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WebhookEventDto {

    private String eventId;
    private String eventType;
    private String profileId;
    private String representativeId;
    private String source; // gbg, system, external
    private String version; // webhook payload version
    private Map<String, Object> eventData;
    private Map<String, String> headers;
    private String rawPayload;
    private String signature;
    private Boolean signatureVerified;
    private String processingStatus; // pending, processing, completed, failed, retrying, skipped
    private String processingResult;
    private String errorMessage;
    private Integer retryCount;
    private Integer maxRetries;
    private LocalDateTime nextRetryAt;
    private LocalDateTime receivedAt;
    private LocalDateTime processedAt;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private WebhookMetricsDto metrics;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WebhookMetricsDto {
        private Long processingTimeMs;
        private Long queueTimeMs;
        private Long totalTimeMs;
        private String processingNode;
        private Integer attemptNumber;
        private Boolean wasRetried;
        private String failureReason;
        private Map<String, Object> performanceMetrics;
    }
}

/**
 * DTO for webhook processing results
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class WebhookProcessingResultDto {

    private String eventId;
    private String eventType;
    private Boolean success;
    private String status; // completed, failed, skipped, retry_scheduled
    private String message;
    private String errorCode;
    private String errorDetails;
    private Map<String, Object> processingData;
    private LocalDateTime processedAt;
    private Long processingTimeMs;
    private Boolean requiresRetry;
    private LocalDateTime nextRetryAt;
    private Integer retryCount;
    private String processingNode;
    private ActionsTakenDto actionsTaken;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ActionsTakenDto {
        private Boolean profileUpdated;
        private Boolean representativeUpdated;
        private Boolean workflowStateChanged;
        private Boolean notificationsSent;
        private Boolean analysisTriggered;
        private Boolean complianceCheckTriggered;
        private String newWorkflowState;
        private String previousWorkflowState;
        private java.util.List<String> notificationRecipients;
        private java.util.List<String> updatedEntities;
    }
}

/**
 * DTO for webhook configuration
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class WebhookConfigDto {

    private String webhookUrl;
    private String secretKey;
    private Boolean enabled;
    private java.util.List<String> enabledEventTypes;
    private Integer maxRetries;
    private Long retryDelayMs;
    private Long timeoutMs;
    private Boolean verifySignature;
    private String signatureHeader;
    private String eventTypeHeader;
    private Map<String, String> customHeaders;
    private WebhookSecurityDto security;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WebhookSecurityDto {
        private Boolean requireHttps;
        private java.util.List<String> allowedIpRanges;
        private String userAgent;
        private Boolean validateCertificate;
        private Long maxPayloadSize;
        private java.util.List<String> blockedEventTypes;
    }
}
