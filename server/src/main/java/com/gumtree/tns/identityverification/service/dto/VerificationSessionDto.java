package com.gumtree.tns.identityverification.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * DTO for verification session information
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VerificationSessionDto {

    private String representativeId;
    private String verificationType; // idv, id3, digital_footprint
    private String provider; // id_scan, yoti, risk_seal, id3
    private String sessionId;
    private String sessionToken;
    private String iframeUrl;
    private String qrCode;
    private String state; // open, closed, pending, failed
    private VerificationResultDto result;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Map<String, Object> providerSpecificData;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VerificationResultDto {
        private String name;
        private String state; // COMPLETE, PENDING, FAILED
        private String identityStatus; // APPROVED, REJECTED, PENDING
        private String identityFailReason;
        private String livenessStatus; // APPROVED, REJECTED, NOT-PERFORMED-UPLOAD
        private String faceMatchStatus; // APPROVED, REJECTED, NOT-PERFORMED-UPLOAD
        private String faceMatchFailReason;
        private String idRequestedAt;
        private String dobOnDocument;
        private String expirationDate;
        private String expirationStatus;
        private String journeyId;
        private String provider;
        private Boolean canDownloadReport;
        private String canDownloadUntil;
        private Integer trustScore; // For digital footprint
        private Boolean suspicious; // For digital footprint
        private Map<String, Object> additionalData;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DigitalFootprintResultDto {
        private String name;
        private String phoneNumber;
        private Map<String, String> phoneStatuses;
        private String emailAddress;
        private Map<String, String> emailStatuses;
        private String emailAge;
        private String country;
        private String gender;
        private String trustScore;
        private Boolean suspicious;
        private String[] photos;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Id3ResultDto {
        private Integer id;
        private String personUuid;
        private String status; // complete, pending, failed
        private Map<String, Object> request;
        private Map<String, Object>[] response;
        private Id3ConfigDto config;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Id3ConfigDto {
        private Integer id;
        private String name;
        private String description;
        private String id3ProfileId;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
    }
}
