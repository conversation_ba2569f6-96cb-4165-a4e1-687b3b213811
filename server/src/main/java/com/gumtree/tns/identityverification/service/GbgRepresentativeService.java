package com.gumtree.tns.identityverification.service;

import com.gumtree.tns.identityverification.service.dto.RepresentativeRequestDto;
import com.gumtree.tns.identityverification.service.dto.RepresentativeResponseDto;
import com.gumtree.tns.identityverification.service.dto.VerificationSessionDto;

import java.util.List;

/**
 * Service for handling GBG representative operations
 */
public interface GbgRepresentativeService {

    /**
     * Create a new representative for a profile
     * 
     * @param profileUuid the GBG profile UUID
     * @param request the representative creation request
     * @return the created representative
     */
    RepresentativeResponseDto createRepresentative(String profileUuid, RepresentativeRequestDto request);

    /**
     * Get representative by UUID
     * 
     * @param representativeUuid the representative UUID
     * @return the representative details
     */
    RepresentativeResponseDto getRepresentative(String representativeUuid);

    /**
     * Get all representatives for a profile
     * 
     * @param profileUuid the GBG profile UUID
     * @return list of representatives
     */
    List<RepresentativeResponseDto> getRepresentativesByProfile(String profileUuid);

    /**
     * Update representative information
     * 
     * @param representativeUuid the representative UUID
     * @param request the update request
     * @return the updated representative
     */
    RepresentativeResponseDto updateRepresentative(String representativeUuid, RepresentativeRequestDto request);

    /**
     * Delete representative
     * 
     * @param representativeUuid the representative UUID
     */
    void deleteRepresentative(String representativeUuid);

    /**
     * Get verification session for a representative
     * 
     * @param representativeUuid the representative UUID
     * @param verificationType the type of verification (idv, id3, digital_footprint)
     * @return verification session details
     */
    VerificationSessionDto getVerificationSession(String representativeUuid, String verificationType);

    /**
     * Get all verifications for a representative
     * 
     * @param representativeUuid the representative UUID
     * @return list of verification sessions
     */
    List<VerificationSessionDto> getRepresentativeVerifications(String representativeUuid);

    /**
     * Update verification status (typically called by webhook or polling)
     * 
     * @param representativeUuid the representative UUID
     * @param verificationType the verification type
     * @param sessionId the verification session ID
     * @return updated verification session
     */
    VerificationSessionDto updateVerificationStatus(String representativeUuid, String verificationType, String sessionId);

    /**
     * Check if representative has completed all required verifications
     * 
     * @param representativeUuid the representative UUID
     * @return true if all required verifications are completed
     */
    boolean isRepresentativeFullyVerified(String representativeUuid);

    /**
     * Get representatives by email
     * 
     * @param email the email address
     * @return list of representatives with matching email
     */
    List<RepresentativeResponseDto> getRepresentativesByEmail(String email);

    /**
     * Search representatives by name
     * 
     * @param firstName the first name (optional)
     * @param lastName the last name (optional)
     * @return list of matching representatives
     */
    List<RepresentativeResponseDto> searchRepresentativesByName(String firstName, String lastName);
}
