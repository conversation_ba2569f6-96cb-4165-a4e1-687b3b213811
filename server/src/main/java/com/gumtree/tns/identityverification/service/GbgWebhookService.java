package com.gumtree.tns.identityverification.service;

import com.gumtree.tns.identityverification.service.dto.WebhookEventDto;
import com.gumtree.tns.identityverification.service.dto.WebhookProcessingResultDto;

import java.util.List;

/**
 * Service for handling GBG webhook events
 */
public interface GbgWebhookService {

    /**
     * Process incoming webhook event
     * 
     * @param eventPayload the raw webhook payload
     * @param signature the webhook signature for verification
     * @param eventType the event type header
     * @return processing result
     */
    WebhookProcessingResultDto processWebhookEvent(String eventPayload, String signature, String eventType);

    /**
     * Verify webhook signature
     * 
     * @param payload the webhook payload
     * @param signature the provided signature
     * @return true if signature is valid
     */
    boolean verifyWebhookSignature(String payload, String signature);

    /**
     * Handle profile created event
     * 
     * @param event the webhook event
     * @return processing result
     */
    WebhookProcessingResultDto handleProfileCreated(WebhookEventDto event);

    /**
     * <PERSON>le profile updated event
     * 
     * @param event the webhook event
     * @return processing result
     */
    WebhookProcessingResultDto handleProfileUpdated(WebhookEventDto event);

    /**
     * Handle representative verification completed event
     * 
     * @param event the webhook event
     * @return processing result
     */
    WebhookProcessingResultDto handleRepresentativeVerificationCompleted(WebhookEventDto event);

    /**
     * Handle analysis completed event
     * 
     * @param event the webhook event
     * @return processing result
     */
    WebhookProcessingResultDto handleAnalysisCompleted(WebhookEventDto event);

    /**
     * Handle checklist updated event
     * 
     * @param event the webhook event
     * @return processing result
     */
    WebhookProcessingResultDto handleChecklistUpdated(WebhookEventDto event);

    /**
     * Handle portal user activity event
     * 
     * @param event the webhook event
     * @return processing result
     */
    WebhookProcessingResultDto handlePortalUserActivity(WebhookEventDto event);

    /**
     * Retry failed webhook processing
     * 
     * @param eventId the event ID to retry
     * @return processing result
     */
    WebhookProcessingResultDto retryWebhookProcessing(String eventId);

    /**
     * Get webhook processing history
     * 
     * @param profileUuid the profile UUID (optional)
     * @param eventType the event type (optional)
     * @param limit maximum number of results
     * @return list of webhook events
     */
    List<WebhookEventDto> getWebhookHistory(String profileUuid, String eventType, Integer limit);

    /**
     * Get failed webhook events that need retry
     * 
     * @param maxRetries maximum retry count to consider
     * @return list of failed events
     */
    List<WebhookEventDto> getFailedWebhookEvents(Integer maxRetries);

    /**
     * Mark webhook event as processed
     * 
     * @param eventId the event ID
     * @param processingResult the processing result
     */
    void markEventAsProcessed(String eventId, WebhookProcessingResultDto processingResult);

    /**
     * Supported webhook event types
     */
    enum WebhookEventType {
        PROFILE_CREATED("profile.created"),
        PROFILE_UPDATED("profile.updated"),
        PROFILE_DELETED("profile.deleted"),
        REPRESENTATIVE_CREATED("representative.created"),
        REPRESENTATIVE_UPDATED("representative.updated"),
        REPRESENTATIVE_VERIFICATION_STARTED("representative.verification.started"),
        REPRESENTATIVE_VERIFICATION_COMPLETED("representative.verification.completed"),
        REPRESENTATIVE_VERIFICATION_FAILED("representative.verification.failed"),
        ANALYSIS_STARTED("analysis.started"),
        ANALYSIS_COMPLETED("analysis.completed"),
        ANALYSIS_FAILED("analysis.failed"),
        CHECKLIST_CREATED("checklist.created"),
        CHECKLIST_UPDATED("checklist.updated"),
        CHECKLIST_COMPLETED("checklist.completed"),
        PORTAL_USER_CREATED("portal_user.created"),
        PORTAL_USER_UPDATED("portal_user.updated"),
        PORTAL_USER_ACTIVITY("portal_user.activity"),
        DOCUMENT_UPLOADED("document.uploaded"),
        DOCUMENT_PROCESSED("document.processed"),
        WORKFLOW_STATE_CHANGED("workflow.state_changed");

        private final String value;

        WebhookEventType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static WebhookEventType fromValue(String value) {
            for (WebhookEventType type : values()) {
                if (type.value.equals(value)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("Unknown webhook event type: " + value);
        }

        /**
         * Check if event type requires immediate processing
         */
        public boolean requiresImmediateProcessing() {
            return this == REPRESENTATIVE_VERIFICATION_COMPLETED ||
                   this == ANALYSIS_COMPLETED ||
                   this == CHECKLIST_COMPLETED ||
                   this == WORKFLOW_STATE_CHANGED;
        }

        /**
         * Check if event type is critical (affects workflow state)
         */
        public boolean isCritical() {
            return this == PROFILE_CREATED ||
                   this == REPRESENTATIVE_VERIFICATION_COMPLETED ||
                   this == ANALYSIS_COMPLETED ||
                   this == CHECKLIST_COMPLETED;
        }
    }

    /**
     * Webhook processing status
     */
    enum ProcessingStatus {
        PENDING("pending"),
        PROCESSING("processing"),
        COMPLETED("completed"),
        FAILED("failed"),
        RETRYING("retrying"),
        SKIPPED("skipped");

        private final String value;

        ProcessingStatus(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static ProcessingStatus fromValue(String value) {
            for (ProcessingStatus status : values()) {
                if (status.value.equals(value)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("Unknown processing status: " + value);
        }
    }
}
