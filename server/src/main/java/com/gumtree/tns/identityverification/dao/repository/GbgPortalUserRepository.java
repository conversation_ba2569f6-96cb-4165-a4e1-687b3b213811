package com.gumtree.tns.identityverification.dao.repository;

import com.gumtree.tns.identityverification.dao.model.GbgPortalUser;
import com.gumtree.tns.identityverification.dao.model.GbgProfile;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository for GBG Portal User operations
 */
@Repository
public interface GbgPortalUserRepository extends JpaRepository<GbgPortalUser, UUID> {

    /**
     * Find portal user by GBG user ID
     */
    Optional<GbgPortalUser> findByGbgUserId(Integer gbgUserId);

    /**
     * Find portal user by email
     */
    Optional<GbgPortalUser> findByEmail(String email);

    /**
     * Find portal users by profile
     */
    List<GbgPortalUser> findByGbgProfileOrderByCreatedAtDesc(GbgProfile gbgProfile);

    /**
     * Find portal users by profile UUID
     */
    @Query("SELECT u FROM GbgPortalUser u WHERE u.gbgProfile.gbgProfileUuid = :profileUuid ORDER BY u.createdAt DESC")
    List<GbgPortalUser> findByProfileUuidOrderByCreatedAtDesc(@Param("profileUuid") String profileUuid);

    /**
     * Find portal users by status
     */
    List<GbgPortalUser> findByStatusOrderByCreatedAtDesc(GbgPortalUser.PortalUserStatus status);

    /**
     * Find portal users by role
     */
    List<GbgPortalUser> findByRoleOrderByCreatedAtDesc(GbgPortalUser.PortalUserRole role);

    /**
     * Find portal users with pagination and filtering
     */
    @Query("SELECT u FROM GbgPortalUser u WHERE " +
           "(:email IS NULL OR LOWER(u.email) LIKE LOWER(CONCAT('%', :email, '%'))) AND " +
           "(:profileId IS NULL OR u.gbgProfile.gbgProfileUuid = :profileId) AND " +
           "(:status IS NULL OR u.status = :status) AND " +
           "(:role IS NULL OR u.role = :role)")
    Page<GbgPortalUser> findPortalUsersWithFilters(
        @Param("email") String email,
        @Param("profileId") String profileId,
        @Param("status") GbgPortalUser.PortalUserStatus status,
        @Param("role") GbgPortalUser.PortalUserRole role,
        Pageable pageable);

    /**
     * Find portal users by name (case-insensitive)
     */
    @Query("SELECT u FROM GbgPortalUser u WHERE LOWER(u.name) LIKE LOWER(CONCAT('%', :name, '%')) ORDER BY u.createdAt DESC")
    List<GbgPortalUser> findByNameContainingIgnoreCase(@Param("name") String name);

    /**
     * Find active portal users
     */
    @Query("SELECT u FROM GbgPortalUser u WHERE u.status = 'ACTIVE' ORDER BY u.lastLoginAt DESC NULLS LAST")
    List<GbgPortalUser> findActiveUsers();

    /**
     * Find inactive portal users (no login for specified days)
     */
    @Query("SELECT u FROM GbgPortalUser u WHERE u.status = 'ACTIVE' AND " +
           "(u.lastLoginAt IS NULL OR u.lastLoginAt < :cutoffDate) ORDER BY u.lastLoginAt ASC NULLS FIRST")
    List<GbgPortalUser> findInactiveUsers(@Param("cutoffDate") LocalDateTime cutoffDate);

    /**
     * Find portal users with pending invitations
     */
    @Query("SELECT u FROM GbgPortalUser u WHERE u.invitationSentAt IS NOT NULL AND u.invitationAcceptedAt IS NULL " +
           "ORDER BY u.invitationSentAt DESC")
    List<GbgPortalUser> findUsersWithPendingInvitations();

    /**
     * Find portal users created within date range
     */
    @Query("SELECT u FROM GbgPortalUser u WHERE u.createdAt >= :startDate AND u.createdAt <= :endDate ORDER BY u.createdAt DESC")
    List<GbgPortalUser> findByCreatedAtBetweenOrderByCreatedAtDesc(
        @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * Find portal users who logged in within date range
     */
    @Query("SELECT u FROM GbgPortalUser u WHERE u.lastLoginAt >= :startDate AND u.lastLoginAt <= :endDate ORDER BY u.lastLoginAt DESC")
    List<GbgPortalUser> findByLastLoginAtBetweenOrderByLastLoginAtDesc(
        @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);

    /**
     * Count portal users by status
     */
    Long countByStatus(GbgPortalUser.PortalUserStatus status);

    /**
     * Count portal users by role
     */
    Long countByRole(GbgPortalUser.PortalUserRole role);

    /**
     * Count portal users by profile
     */
    Long countByGbgProfile(GbgProfile gbgProfile);

    /**
     * Count active portal users
     */
    @Query("SELECT COUNT(u) FROM GbgPortalUser u WHERE u.status = 'ACTIVE'")
    Long countActiveUsers();

    /**
     * Count portal users who logged in recently
     */
    @Query("SELECT COUNT(u) FROM GbgPortalUser u WHERE u.lastLoginAt >= :since")
    Long countRecentlyActiveUsers(@Param("since") LocalDateTime since);

    /**
     * Find most active portal users (by login frequency)
     */
    @Query("SELECT u FROM GbgPortalUser u WHERE u.status = 'ACTIVE' AND u.lastLoginAt IS NOT NULL " +
           "ORDER BY u.lastLoginAt DESC")
    List<GbgPortalUser> findMostActiveUsers(Pageable pageable);

    /**
     * Check if email exists
     */
    boolean existsByEmail(String email);

    /**
     * Check if GBG user ID exists
     */
    boolean existsByGbgUserId(Integer gbgUserId);

    /**
     * Find portal users by language
     */
    List<GbgPortalUser> findByLanguageOrderByCreatedAtDesc(String language);

    /**
     * Find portal users by timezone
     */
    List<GbgPortalUser> findByTimezoneOrderByCreatedAtDesc(String timezone);

    /**
     * Find portal users with email notifications enabled
     */
    @Query("SELECT u FROM GbgPortalUser u WHERE u.emailNotifications = true AND u.status = 'ACTIVE' ORDER BY u.createdAt DESC")
    List<GbgPortalUser> findUsersWithEmailNotificationsEnabled();

    /**
     * Find portal users with SMS notifications enabled
     */
    @Query("SELECT u FROM GbgPortalUser u WHERE u.smsNotifications = true AND u.status = 'ACTIVE' ORDER BY u.createdAt DESC")
    List<GbgPortalUser> findUsersWithSmsNotificationsEnabled();

    /**
     * Find portal users by notification frequency
     */
    List<GbgPortalUser> findByNotificationFrequencyOrderByCreatedAtDesc(String notificationFrequency);

    /**
     * Get average days since last login for active users
     */
    @Query("SELECT AVG(DATEDIFF(CURRENT_DATE, u.lastLoginAt)) FROM GbgPortalUser u WHERE " +
           "u.status = 'ACTIVE' AND u.lastLoginAt IS NOT NULL")
    Double getAverageDaysSinceLastLogin();

    /**
     * Find portal users who need to be reminded about pending invitations
     */
    @Query("SELECT u FROM GbgPortalUser u WHERE u.invitationSentAt IS NOT NULL AND u.invitationAcceptedAt IS NULL " +
           "AND u.invitationSentAt < :reminderCutoff ORDER BY u.invitationSentAt ASC")
    List<GbgPortalUser> findUsersNeedingInvitationReminder(@Param("reminderCutoff") LocalDateTime reminderCutoff);

    /**
     * Delete old inactive portal users (cleanup)
     */
    @Query("DELETE FROM GbgPortalUser u WHERE u.status = 'INACTIVE' AND u.updatedAt < :cutoffDate")
    void deleteOldInactiveUsers(@Param("cutoffDate") LocalDateTime cutoffDate);
}
