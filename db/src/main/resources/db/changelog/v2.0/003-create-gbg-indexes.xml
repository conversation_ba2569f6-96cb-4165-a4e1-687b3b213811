<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <!-- Indexes for gbg_profiles table -->
    <changeSet id="001-create-gbg-profiles-indexes" author="identity-verification-service">
        <createIndex tableName="gbg_profiles" indexName="idx_gbg_profiles_gbg_uuid">
            <column name="gbg_profile_uuid"/>
        </createIndex>
        
        <createIndex tableName="gbg_profiles" indexName="idx_gbg_profiles_customer_ref">
            <column name="customer_reference"/>
        </createIndex>
        
        <createIndex tableName="gbg_profiles" indexName="idx_gbg_profiles_review_status">
            <column name="review_status"/>
        </createIndex>
        
        <createIndex tableName="gbg_profiles" indexName="idx_gbg_profiles_risk_category">
            <column name="risk_category"/>
        </createIndex>
        
        <createIndex tableName="gbg_profiles" indexName="idx_gbg_profiles_team_id">
            <column name="team_id"/>
        </createIndex>
        
        <createIndex tableName="gbg_profiles" indexName="idx_gbg_profiles_assignee_id">
            <column name="assignee_id"/>
        </createIndex>
        
        <createIndex tableName="gbg_profiles" indexName="idx_gbg_profiles_created_at">
            <column name="created_at"/>
        </createIndex>
        
        <createIndex tableName="gbg_profiles" indexName="idx_gbg_profiles_updated_at">
            <column name="updated_at"/>
        </createIndex>
    </changeSet>

    <!-- Indexes for gbg_representatives table -->
    <changeSet id="002-create-gbg-representatives-indexes" author="identity-verification-service">
        <createIndex tableName="gbg_representatives" indexName="idx_gbg_representatives_gbg_uuid">
            <column name="gbg_representative_uuid"/>
        </createIndex>
        
        <createIndex tableName="gbg_representatives" indexName="idx_gbg_representatives_profile_id">
            <column name="gbg_profile_id"/>
        </createIndex>
        
        <createIndex tableName="gbg_representatives" indexName="idx_gbg_representatives_customer_ref">
            <column name="customer_reference"/>
        </createIndex>
        
        <createIndex tableName="gbg_representatives" indexName="idx_gbg_representatives_email">
            <column name="email"/>
        </createIndex>
        
        <createIndex tableName="gbg_representatives" indexName="idx_gbg_representatives_full_name">
            <column name="full_name"/>
        </createIndex>
        
        <createIndex tableName="gbg_representatives" indexName="idx_gbg_representatives_is_company">
            <column name="is_company"/>
        </createIndex>
        
        <createIndex tableName="gbg_representatives" indexName="idx_gbg_representatives_created_at">
            <column name="created_at"/>
        </createIndex>
        
        <!-- GIN index for representative_types JSONB column -->
        <sql>
            CREATE INDEX idx_gbg_representatives_types_gin ON gbg_representatives USING GIN (representative_types);
        </sql>
    </changeSet>

    <!-- Indexes for gbg_representative_verifications table -->
    <changeSet id="003-create-gbg-verifications-indexes" author="identity-verification-service">
        <createIndex tableName="gbg_representative_verifications" indexName="idx_gbg_verifications_representative_id">
            <column name="gbg_representative_id"/>
        </createIndex>
        
        <createIndex tableName="gbg_representative_verifications" indexName="idx_gbg_verifications_type">
            <column name="verification_type"/>
        </createIndex>
        
        <createIndex tableName="gbg_representative_verifications" indexName="idx_gbg_verifications_provider">
            <column name="provider"/>
        </createIndex>
        
        <createIndex tableName="gbg_representative_verifications" indexName="idx_gbg_verifications_state">
            <column name="state"/>
        </createIndex>
        
        <createIndex tableName="gbg_representative_verifications" indexName="idx_gbg_verifications_session_id">
            <column name="session_id"/>
        </createIndex>
        
        <createIndex tableName="gbg_representative_verifications" indexName="idx_gbg_verifications_created_at">
            <column name="created_at"/>
        </createIndex>
    </changeSet>

    <!-- Indexes for gbg_portal_users table -->
    <changeSet id="004-create-gbg-portal-users-indexes" author="identity-verification-service">
        <createIndex tableName="gbg_portal_users" indexName="idx_gbg_portal_users_gbg_user_id">
            <column name="gbg_user_id"/>
        </createIndex>
        
        <createIndex tableName="gbg_portal_users" indexName="idx_gbg_portal_users_profile_id">
            <column name="gbg_profile_id"/>
        </createIndex>
        
        <createIndex tableName="gbg_portal_users" indexName="idx_gbg_portal_users_email">
            <column name="email"/>
        </createIndex>
        
        <createIndex tableName="gbg_portal_users" indexName="idx_gbg_portal_users_role">
            <column name="role"/>
        </createIndex>
    </changeSet>

    <!-- Indexes for gbg_analysis_results table -->
    <changeSet id="005-create-gbg-analysis-indexes" author="identity-verification-service">
        <createIndex tableName="gbg_analysis_results" indexName="idx_gbg_analysis_profile_id">
            <column name="gbg_profile_id"/>
        </createIndex>
        
        <createIndex tableName="gbg_analysis_results" indexName="idx_gbg_analysis_status">
            <column name="status"/>
        </createIndex>
        
        <createIndex tableName="gbg_analysis_results" indexName="idx_gbg_analysis_risk_level">
            <column name="risk_level"/>
        </createIndex>
        
        <createIndex tableName="gbg_analysis_results" indexName="idx_gbg_analysis_score">
            <column name="score"/>
        </createIndex>
        
        <createIndex tableName="gbg_analysis_results" indexName="idx_gbg_analysis_created_at">
            <column name="created_at"/>
        </createIndex>
    </changeSet>

    <!-- Indexes for gbg_checklists table -->
    <changeSet id="006-create-gbg-checklists-indexes" author="identity-verification-service">
        <createIndex tableName="gbg_checklists" indexName="idx_gbg_checklists_profile_id">
            <column name="gbg_profile_id"/>
        </createIndex>
        
        <createIndex tableName="gbg_checklists" indexName="idx_gbg_checklists_gbg_id">
            <column name="gbg_checklist_id"/>
        </createIndex>
        
        <createIndex tableName="gbg_checklists" indexName="idx_gbg_checklists_name">
            <column name="name"/>
        </createIndex>
    </changeSet>

    <!-- Indexes for gbg_forms table -->
    <changeSet id="007-create-gbg-forms-indexes" author="identity-verification-service">
        <createIndex tableName="gbg_forms" indexName="idx_gbg_forms_profile_id">
            <column name="gbg_profile_id"/>
        </createIndex>
        
        <createIndex tableName="gbg_forms" indexName="idx_gbg_forms_gbg_id">
            <column name="gbg_form_id"/>
        </createIndex>
        
        <createIndex tableName="gbg_forms" indexName="idx_gbg_forms_label">
            <column name="label"/>
        </createIndex>
        
        <createIndex tableName="gbg_forms" indexName="idx_gbg_forms_reference">
            <column name="reference"/>
        </createIndex>
    </changeSet>

    <!-- Indexes for gbg_documents table -->
    <changeSet id="008-create-gbg-documents-indexes" author="identity-verification-service">
        <createIndex tableName="gbg_documents" indexName="idx_gbg_documents_profile_id">
            <column name="gbg_profile_id"/>
        </createIndex>
        
        <createIndex tableName="gbg_documents" indexName="idx_gbg_documents_gbg_id">
            <column name="gbg_document_id"/>
        </createIndex>
        
        <createIndex tableName="gbg_documents" indexName="idx_gbg_documents_category">
            <column name="category"/>
        </createIndex>
        
        <createIndex tableName="gbg_documents" indexName="idx_gbg_documents_name">
            <column name="name"/>
        </createIndex>
        
        <createIndex tableName="gbg_documents" indexName="idx_gbg_documents_created_at">
            <column name="created_at"/>
        </createIndex>
    </changeSet>

    <!-- Indexes for gbg_domains table -->
    <changeSet id="009-create-gbg-domains-indexes" author="identity-verification-service">
        <createIndex tableName="gbg_domains" indexName="idx_gbg_domains_profile_id">
            <column name="gbg_profile_id"/>
        </createIndex>
        
        <createIndex tableName="gbg_domains" indexName="idx_gbg_domains_gbg_id">
            <column name="gbg_domain_id"/>
        </createIndex>
        
        <createIndex tableName="gbg_domains" indexName="idx_gbg_domains_domain">
            <column name="domain"/>
        </createIndex>
        
        <createIndex tableName="gbg_domains" indexName="idx_gbg_domains_type">
            <column name="domain_type"/>
        </createIndex>
    </changeSet>

    <!-- Indexes for gbg_portal_user_activity table -->
    <changeSet id="010-create-gbg-activity-indexes" author="identity-verification-service">
        <createIndex tableName="gbg_portal_user_activity" indexName="idx_gbg_activity_gbg_id">
            <column name="gbg_activity_id"/>
        </createIndex>
        
        <createIndex tableName="gbg_portal_user_activity" indexName="idx_gbg_activity_user_id">
            <column name="gbg_portal_user_id"/>
        </createIndex>
        
        <createIndex tableName="gbg_portal_user_activity" indexName="idx_gbg_activity_profile_id">
            <column name="gbg_profile_id"/>
        </createIndex>
        
        <createIndex tableName="gbg_portal_user_activity" indexName="idx_gbg_activity_type">
            <column name="activity_type"/>
        </createIndex>
        
        <createIndex tableName="gbg_portal_user_activity" indexName="idx_gbg_activity_ip">
            <column name="ip_address"/>
        </createIndex>
        
        <createIndex tableName="gbg_portal_user_activity" indexName="idx_gbg_activity_created_at">
            <column name="created_at"/>
        </createIndex>
    </changeSet>

    <!-- Indexes for gbg_search_history table -->
    <changeSet id="011-create-gbg-search-indexes" author="identity-verification-service">
        <createIndex tableName="gbg_search_history" indexName="idx_gbg_search_lookup_id">
            <column name="lookup_id"/>
        </createIndex>
        
        <createIndex tableName="gbg_search_history" indexName="idx_gbg_search_type">
            <column name="search_type"/>
        </createIndex>
        
        <createIndex tableName="gbg_search_history" indexName="idx_gbg_search_user_id">
            <column name="created_by_user_id"/>
        </createIndex>
        
        <createIndex tableName="gbg_search_history" indexName="idx_gbg_search_account_id">
            <column name="created_by_account_id"/>
        </createIndex>
        
        <createIndex tableName="gbg_search_history" indexName="idx_gbg_search_created_at">
            <column name="created_at"/>
        </createIndex>
        
        <!-- GIN index for search_criteria and search_results JSONB columns -->
        <sql>
            CREATE INDEX idx_gbg_search_criteria_gin ON gbg_search_history USING GIN (search_criteria);
        </sql>
        
        <sql>
            CREATE INDEX idx_gbg_search_results_gin ON gbg_search_history USING GIN (search_results);
        </sql>
    </changeSet>

    <!-- Update existing indexes for identity_verification_requests -->
    <changeSet id="012-create-verification-requests-gbg-indexes" author="identity-verification-service">
        <createIndex tableName="identity_verification_requests" indexName="idx_verification_requests_gbg_profile_internal">
            <column name="gbg_profile_internal_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
