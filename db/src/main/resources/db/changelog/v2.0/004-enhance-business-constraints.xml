<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <!-- Add direct user/account association to GBG profiles -->
    <changeSet id="001-add-user-account-to-gbg-profiles" author="identity-verification-service">
        <addColumn tableName="gbg_profiles">
            <column name="platform_user_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="platform_account_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="initiated_by_user_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="verification_purpose" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        
        <!-- Ensure either user_id or account_id is set, but not both -->
        <addCheckConstraint tableName="gbg_profiles" 
                           constraintName="chk_profile_user_or_account" 
                           checkCondition="(platform_user_id IS NOT NULL AND platform_account_id IS NULL) OR (platform_user_id IS NULL AND platform_account_id IS NOT NULL)"/>
        
        <!-- Ensure verification purpose matches profile type -->
        <addCheckConstraint tableName="gbg_profiles" 
                           constraintName="chk_profile_purpose_type_match" 
                           checkCondition="(profile_type = 'KYC' AND verification_purpose IN ('individual_verification', 'account_holder_verification')) OR (profile_type = 'KYB' AND verification_purpose IN ('business_verification', 'merchant_onboarding', 'corporate_account'))"/>
        
        <addCheckConstraint tableName="gbg_profiles" 
                           constraintName="chk_verification_purpose" 
                           checkCondition="verification_purpose IN ('individual_verification', 'account_holder_verification', 'business_verification', 'merchant_onboarding', 'corporate_account')"/>
    </changeSet>

    <!-- Create verification workflow state management table -->
    <changeSet id="002-create-verification-workflow-states" author="identity-verification-service">
        <createTable tableName="verification_workflow_states">
            <column name="id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="gbg_profile_id" type="UUID">
                <constraints nullable="false" foreignKeyName="fk_workflow_states_profile" references="gbg_profiles(id)"/>
            </column>
            <column name="current_state" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="previous_state" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="state_data" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="can_progress" type="BOOLEAN" defaultValueBoolean="true">
                <constraints nullable="false"/>
            </column>
            <column name="blocking_reasons" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="next_required_actions" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="estimated_completion_date" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="true"/>
            </column>
            <column name="state_changed_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="state_changed_by" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        
        <addCheckConstraint tableName="verification_workflow_states" 
                           constraintName="chk_workflow_current_state" 
                           checkCondition="current_state IN ('INITIATED', 'COMPANY_SEARCH_COMPLETED', 'PROFILE_CREATED', 'REPRESENTATIVES_ADDED', 'IDENTITY_VERIFICATION_IN_PROGRESS', 'IDENTITY_VERIFICATION_COMPLETED', 'RISK_ANALYSIS_IN_PROGRESS', 'RISK_ANALYSIS_COMPLETED', 'COMPLIANCE_CHECK_IN_PROGRESS', 'COMPLIANCE_CHECK_COMPLETED', 'MANUAL_REVIEW_REQUIRED', 'APPROVED', 'REJECTED', 'EXPIRED', 'CANCELLED')"/>
        
        <!-- Ensure only one active workflow state per profile -->
        <addUniqueConstraint tableName="verification_workflow_states" 
                            columnNames="gbg_profile_id" 
                            constraintName="uk_workflow_states_profile"/>
    </changeSet>

    <!-- Create workflow state transition history -->
    <changeSet id="003-create-workflow-state-history" author="identity-verification-service">
        <createTable tableName="verification_workflow_state_history">
            <column name="id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="gbg_profile_id" type="UUID">
                <constraints nullable="false" foreignKeyName="fk_workflow_history_profile" references="gbg_profiles(id)"/>
            </column>
            <column name="from_state" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="to_state" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="transition_reason" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
            <column name="transition_data" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="triggered_by" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="triggered_by_system" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
            <column name="duration_in_previous_state_ms" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <!-- Add business rule validation table -->
    <changeSet id="004-create-business-rule-validations" author="identity-verification-service">
        <createTable tableName="business_rule_validations">
            <column name="id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="gbg_profile_id" type="UUID">
                <constraints nullable="false" foreignKeyName="fk_business_rules_profile" references="gbg_profiles(id)"/>
            </column>
            <column name="rule_name" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="rule_category" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="validation_status" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="validation_result" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="error_message" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="is_blocking" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
            <column name="retry_count" type="INTEGER" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="last_validated_at" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="true"/>
            </column>
            <column name="next_validation_at" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="true"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        
        <addCheckConstraint tableName="business_rule_validations" 
                           constraintName="chk_rule_category" 
                           checkCondition="rule_category IN ('data_completeness', 'data_quality', 'business_logic', 'compliance', 'security', 'integration')"/>
        
        <addCheckConstraint tableName="business_rule_validations" 
                           constraintName="chk_validation_status" 
                           checkCondition="validation_status IN ('PENDING', 'PASSED', 'FAILED', 'SKIPPED', 'ERROR')"/>
        
        <addUniqueConstraint tableName="business_rule_validations" 
                            columnNames="gbg_profile_id,rule_name" 
                            constraintName="uk_business_rules_profile_rule"/>
    </changeSet>

    <!-- Update identity_verification_requests with better constraints -->
    <changeSet id="005-enhance-verification-request-constraints" author="identity-verification-service">
        <!-- Add check constraint to ensure either user_id or account_id is set -->
        <addCheckConstraint tableName="identity_verification_requests" 
                           constraintName="chk_verification_user_or_account" 
                           checkCondition="(user_id IS NOT NULL AND account_id IS NULL) OR (user_id IS NULL AND account_id IS NOT NULL)"/>
        
        <!-- Add constraint to ensure verification type matches user/account type -->
        <addCheckConstraint tableName="identity_verification_requests" 
                           constraintName="chk_verification_type_match" 
                           checkCondition="(user_id IS NOT NULL AND verification_type = 'KYC') OR (account_id IS NOT NULL AND verification_type = 'KYB')"/>
        
        <!-- Ensure only one active verification per user/account -->
        <sql>
            CREATE UNIQUE INDEX uk_active_verification_user 
            ON identity_verification_requests (user_id) 
            WHERE user_id IS NOT NULL AND status NOT IN ('COMPLETED', 'APPROVED', 'REJECTED', 'EXPIRED', 'CANCELLED');
        </sql>
        
        <sql>
            CREATE UNIQUE INDEX uk_active_verification_account 
            ON identity_verification_requests (account_id) 
            WHERE account_id IS NOT NULL AND status NOT IN ('COMPLETED', 'APPROVED', 'REJECTED', 'EXPIRED', 'CANCELLED');
        </sql>
    </changeSet>

    <!-- Add audit trail enhancements -->
    <changeSet id="006-enhance-audit-trail" author="identity-verification-service">
        <addColumn tableName="gbg_profiles">
            <column name="last_accessed_at" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="true"/>
            </column>
            <column name="last_accessed_by" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="access_count" type="INTEGER" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        
        <addColumn tableName="gbg_representatives">
            <column name="last_accessed_at" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="true"/>
            </column>
            <column name="last_accessed_by" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

</databaseChangeLog>
