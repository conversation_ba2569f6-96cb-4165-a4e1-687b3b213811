<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <!-- GBG Analysis Results Table -->
    <changeSet id="001-create-gbg-analysis-results" author="identity-verification-service">
        <createTable tableName="gbg_analysis_results">
            <column name="id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="gbg_profile_id" type="UUID">
                <constraints nullable="false" foreignKeyName="fk_analysis_results_profile" references="gbg_profiles(id)"/>
            </column>
            <column name="analysis_id" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="score" type="INTEGER">
                <constraints nullable="true"/>
            </column>
            <column name="status" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="risk_level" type="VARCHAR(20)">
                <constraints nullable="true"/>
            </column>
            <column name="analysis_report" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        
        <addCheckConstraint tableName="gbg_analysis_results" 
                           constraintName="chk_analysis_status" 
                           checkCondition="status IN ('failed', 'completed', 'pending', 'missing_data')"/>
        
        <addCheckConstraint tableName="gbg_analysis_results" 
                           constraintName="chk_analysis_risk_level" 
                           checkCondition="risk_level IS NULL OR risk_level IN ('Low', 'Medium', 'High')"/>
    </changeSet>

    <!-- GBG Checklists Table -->
    <changeSet id="002-create-gbg-checklists" author="identity-verification-service">
        <createTable tableName="gbg_checklists">
            <column name="id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="gbg_profile_id" type="UUID">
                <constraints nullable="false" foreignKeyName="fk_checklists_profile" references="gbg_profiles(id)"/>
            </column>
            <column name="gbg_checklist_id" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="uncompleted_tasks" type="INTEGER" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="completed_tasks" type="INTEGER" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="sections_data" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        
        <addUniqueConstraint tableName="gbg_checklists" 
                            columnNames="gbg_profile_id,gbg_checklist_id" 
                            constraintName="uk_profile_checklist"/>
    </changeSet>

    <!-- GBG Forms Table -->
    <changeSet id="003-create-gbg-forms" author="identity-verification-service">
        <createTable tableName="gbg_forms">
            <column name="id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="gbg_profile_id" type="UUID">
                <constraints nullable="false" foreignKeyName="fk_forms_profile" references="gbg_profiles(id)"/>
            </column>
            <column name="gbg_form_id" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="label" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="reference" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="answers_data" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        
        <addUniqueConstraint tableName="gbg_forms" 
                            columnNames="gbg_profile_id,gbg_form_id" 
                            constraintName="uk_profile_form"/>
    </changeSet>

    <!-- GBG Documents Table -->
    <changeSet id="004-create-gbg-documents" author="identity-verification-service">
        <createTable tableName="gbg_documents">
            <column name="id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="gbg_profile_id" type="UUID">
                <constraints nullable="false" foreignKeyName="fk_gbg_documents_profile" references="gbg_profiles(id)"/>
            </column>
            <column name="gbg_document_id" type="INTEGER">
                <constraints nullable="false"/>
            </column>
            <column name="library_id" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="category" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="file_type" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="download_link" type="VARCHAR(1000)">
                <constraints nullable="true"/>
            </column>
            <column name="link_expires_at" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="true"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        
        <addUniqueConstraint tableName="gbg_documents" 
                            columnNames="gbg_profile_id,gbg_document_id" 
                            constraintName="uk_profile_document"/>
    </changeSet>

    <!-- GBG Domain Information Table -->
    <changeSet id="005-create-gbg-domains" author="identity-verification-service">
        <createTable tableName="gbg_domains">
            <column name="id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="gbg_profile_id" type="UUID">
                <constraints nullable="false" foreignKeyName="fk_domains_profile" references="gbg_profiles(id)"/>
            </column>
            <column name="gbg_domain_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="domain_type" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="domain" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="whois_data" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="social_data" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="online_presence_data" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="scam_advisor_data" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        
        <addCheckConstraint tableName="gbg_domains" 
                           constraintName="chk_domain_type" 
                           checkCondition="domain_type IN ('portal', 'api_import')"/>
        
        <addUniqueConstraint tableName="gbg_domains" 
                            columnNames="gbg_profile_id,gbg_domain_id" 
                            constraintName="uk_profile_domain"/>
    </changeSet>

    <!-- GBG Portal User Activity Table -->
    <changeSet id="006-create-gbg-portal-user-activity" author="identity-verification-service">
        <createTable tableName="gbg_portal_user_activity">
            <column name="id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="gbg_activity_id" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="gbg_portal_user_id" type="UUID">
                <constraints nullable="false" foreignKeyName="fk_activity_portal_user" references="gbg_portal_users(id)"/>
            </column>
            <column name="gbg_profile_id" type="UUID">
                <constraints nullable="true" foreignKeyName="fk_activity_profile" references="gbg_profiles(id)"/>
            </column>
            <column name="activity_type" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="ip_address" type="VARCHAR(45)">
                <constraints nullable="true"/>
            </column>
            <column name="user_agent" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="activity_details" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        
        <addCheckConstraint tableName="gbg_portal_user_activity" 
                           constraintName="chk_activity_type" 
                           checkCondition="activity_type IN ('access_log', 'form_submission', 'document_upload', 'profile_update')"/>
    </changeSet>

    <!-- GBG Search History Table -->
    <changeSet id="007-create-gbg-search-history" author="identity-verification-service">
        <createTable tableName="gbg_search_history">
            <column name="id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="lookup_id" type="INTEGER">
                <constraints nullable="true"/>
            </column>
            <column name="search_type" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="search_criteria" type="JSONB">
                <constraints nullable="false"/>
            </column>
            <column name="search_results" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="result_count" type="INTEGER" defaultValueNumeric="0">
                <constraints nullable="false"/>
            </column>
            <column name="created_by_user_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="created_by_account_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        
        <addCheckConstraint tableName="gbg_search_history" 
                           constraintName="chk_search_type" 
                           checkCondition="search_type IN ('company')"/>
        
        <addCheckConstraint tableName="gbg_search_history" 
                           constraintName="chk_search_user_or_account" 
                           checkCondition="(created_by_user_id IS NOT NULL AND created_by_account_id IS NULL) OR (created_by_user_id IS NULL AND created_by_account_id IS NOT NULL) OR (created_by_user_id IS NULL AND created_by_account_id IS NULL)"/>
    </changeSet>

</databaseChangeLog>
