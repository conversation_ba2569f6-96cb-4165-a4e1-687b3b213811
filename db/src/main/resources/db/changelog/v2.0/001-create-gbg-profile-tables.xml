<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <!-- GBG Profiles Table - Central entity for all GBG operations -->
    <changeSet id="001-create-gbg-profiles" author="identity-verification-service">
        <createTable tableName="gbg_profiles">
            <column name="id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="gbg_profile_uuid" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="customer_reference" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="profile_type" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="company_name" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
            <column name="company_number" type="VARCHAR(100)">
                <constraints nullable="true"/>
            </column>
            <column name="vat_number" type="VARCHAR(100)">
                <constraints nullable="true"/>
            </column>
            <column name="domain" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="review_status" type="VARCHAR(30)">
                <constraints nullable="false"/>
            </column>
            <column name="risk_id" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="risk_label" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="risk_category" type="VARCHAR(20)">
                <constraints nullable="true"/>
            </column>
            <column name="team_id" type="INTEGER">
                <constraints nullable="true"/>
            </column>
            <column name="team_label" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="assignee_id" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="assignee_name" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="configuration_id" type="INTEGER">
                <constraints nullable="true"/>
            </column>
            <column name="configuration_name" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="address_data" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="company_data" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="onboarding_statuses" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="review_at" type="TIMESTAMP WITH TIME ZONE">
                <constraints nullable="true"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        
        <addCheckConstraint tableName="gbg_profiles" 
                           constraintName="chk_profile_type" 
                           checkCondition="profile_type IN ('KYC', 'KYB')"/>
        
        <addCheckConstraint tableName="gbg_profiles" 
                           constraintName="chk_review_status" 
                           checkCondition="review_status IN ('Processed', 'Needs Review', 'Additional Info Required', 'Approved', 'Declined', 'Archived')"/>
        
        <addCheckConstraint tableName="gbg_profiles" 
                           constraintName="chk_risk_category" 
                           checkCondition="risk_category IS NULL OR risk_category IN ('Low', 'Medium', 'High')"/>
    </changeSet>

    <!-- GBG Representatives Table - Key personnel for companies -->
    <changeSet id="002-create-gbg-representatives" author="identity-verification-service">
        <createTable tableName="gbg_representatives">
            <column name="id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="gbg_representative_uuid" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="gbg_profile_id" type="UUID">
                <constraints nullable="false" foreignKeyName="fk_representatives_profile" references="gbg_profiles(id)"/>
            </column>
            <column name="customer_reference" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="is_company" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
            <column name="representative_types" type="JSONB">
                <constraints nullable="false"/>
            </column>
            <column name="full_name" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
            <column name="first_name" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="middle_name" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="last_name" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="email" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="date_of_birth" type="DATE">
                <constraints nullable="true"/>
            </column>
            <column name="job_title" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="professional_profile" type="VARCHAR(500)">
                <constraints nullable="true"/>
            </column>
            <column name="nationality" type="VARCHAR(100)">
                <constraints nullable="true"/>
            </column>
            <column name="country_of_birth" type="VARCHAR(100)">
                <constraints nullable="true"/>
            </column>
            <column name="birth_country_code" type="VARCHAR(2)">
                <constraints nullable="true"/>
            </column>
            <column name="country_of_residence" type="VARCHAR(100)">
                <constraints nullable="true"/>
            </column>
            <column name="residence_country_code" type="VARCHAR(2)">
                <constraints nullable="true"/>
            </column>
            <column name="personal_number" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="address_data" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="person_type_details" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="copy_address" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="true"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>

    <!-- GBG Representative Verifications Table -->
    <changeSet id="003-create-gbg-representative-verifications" author="identity-verification-service">
        <createTable tableName="gbg_representative_verifications">
            <column name="id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="gbg_representative_id" type="UUID">
                <constraints nullable="false" foreignKeyName="fk_verifications_representative" references="gbg_representatives(id)"/>
            </column>
            <column name="verification_type" type="VARCHAR(30)">
                <constraints nullable="false"/>
            </column>
            <column name="provider" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="session_id" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="session_token" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="iframe_url" type="VARCHAR(1000)">
                <constraints nullable="true"/>
            </column>
            <column name="qr_code" type="VARCHAR(1000)">
                <constraints nullable="true"/>
            </column>
            <column name="state" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="verification_result" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="provider_data" type="JSONB">
                <constraints nullable="true"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        
        <addCheckConstraint tableName="gbg_representative_verifications" 
                           constraintName="chk_verification_type" 
                           checkCondition="verification_type IN ('idv', 'id3', 'digital_footprint')"/>
        
        <addCheckConstraint tableName="gbg_representative_verifications" 
                           constraintName="chk_provider" 
                           checkCondition="provider IN ('id_scan', 'yoti', 'risk_seal', 'id3')"/>
        
        <addCheckConstraint tableName="gbg_representative_verifications" 
                           constraintName="chk_state" 
                           checkCondition="state IN ('open', 'closed', 'pending', 'failed')"/>
    </changeSet>

    <!-- GBG Portal Users Table -->
    <changeSet id="004-create-gbg-portal-users" author="identity-verification-service">
        <createTable tableName="gbg_portal_users">
            <column name="id" type="UUID" defaultValueComputed="gen_random_uuid()">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="gbg_user_id" type="INTEGER">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="gbg_profile_id" type="UUID">
                <constraints nullable="true" foreignKeyName="fk_portal_users_profile" references="gbg_profiles(id)"/>
            </column>
            <column name="email" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>
            <column name="role" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="created_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="updated_at" type="TIMESTAMP WITH TIME ZONE" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
        </createTable>
        
        <addCheckConstraint tableName="gbg_portal_users" 
                           constraintName="chk_role" 
                           checkCondition="role IN ('admin')"/>
    </changeSet>

    <!-- Update identity_verification_requests to link with gbg_profiles -->
    <changeSet id="005-add-gbg-profile-link" author="identity-verification-service">
        <addColumn tableName="identity_verification_requests">
            <column name="gbg_profile_internal_id" type="UUID">
                <constraints nullable="true" foreignKeyName="fk_verification_requests_gbg_profile" references="gbg_profiles(id)"/>
            </column>
        </addColumn>
    </changeSet>

</databaseChangeLog>
