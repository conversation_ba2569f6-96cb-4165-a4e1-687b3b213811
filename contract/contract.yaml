openapi: 3.0.0
info:
  version: 3.0-SNAPSHOT
  title: identity-verification-service
  description: Identity Verification Service for KYC/KYB authentication using GBG integration
tags:
  - name: identityVerification
    description: Identity Verification API for users and accounts
  - name: verificationStatus
    description: Verification status query API
  - name: companySearch
    description: Company search and information lookup API
  - name: analysis
    description: Risk analysis and compliance checking API
  - name: representatives
    description: Representative management for business verification
  - name: gdpr
    description: GDPR compliance API for data access and deletion
  - name: admin
    description: Administrative operations for customer service
paths:
  '/users/{user-id}/identity-verification':
    post:
      tags:
        - identityVerification
      operationId: initiateUserIdentityVerification
      summary: Initiate identity verification for a user
      description: Start KYC process for an individual user
      security:
        - bearerAuth: []
      parameters:
        - name: user-id
          in: path
          description: User ID
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InitiateVerificationRequest'
      responses:
        '201':
          description: Verification initiated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VerificationResponse'
        default:
          $ref: '#/components/responses/ApiErrorResponse'
    get:
      tags:
        - verificationStatus
      operationId: getUserVerificationStatus
      summary: Get user verification status
      description: Retrieve current verification status for a user
      parameters:
        - name: user-id
          in: path
          description: User ID
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Verification status retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VerificationStatusResponse'
        default:
          $ref: '#/components/responses/ApiErrorResponse'
  '/accounts/{account-id}/identity-verification':
    post:
      tags:
        - identityVerification
      operationId: initiateAccountIdentityVerification
      summary: Initiate identity verification for an account
      description: Start KYB process for a business account
      security:
        - bearerAuth: []
      parameters:
        - name: account-id
          in: path
          description: Account ID
          required: true
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InitiateBusinessVerificationRequest'
      responses:
        '201':
          description: Business verification initiated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VerificationResponse'
        default:
          $ref: '#/components/responses/ApiErrorResponse'
    get:
      tags:
        - verificationStatus
      operationId: getAccountVerificationStatus
      summary: Get account verification status
      description: Retrieve current verification status for an account
      parameters:
        - name: account-id
          in: path
          description: Account ID
          required: true
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: Account verification status retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VerificationStatusResponse'
        default:
          $ref: '#/components/responses/ApiErrorResponse'
  '/verification-requests/{verification-id}/documents':
    post:
      tags:
        - identityVerification
      operationId: uploadVerificationDocument
      summary: Upload verification document
      description: Upload document for identity verification process
      security:
        - bearerAuth: []
      parameters:
        - name: verification-id
          in: path
          description: Verification request ID
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DocumentUploadRequest'
      responses:
        '201':
          description: Document uploaded successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentResponse'
        default:
          $ref: '#/components/responses/ApiErrorResponse'
  '/verification-requests/{verification-id}/submit':
    post:
      tags:
        - identityVerification
      operationId: submitVerificationRequest
      summary: Submit verification request for processing
      description: Submit completed verification request to GBG for processing
      security:
        - bearerAuth: []
      parameters:
        - name: verification-id
          in: path
          description: Verification request ID
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Verification request submitted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VerificationResponse'
        default:
          $ref: '#/components/responses/ApiErrorResponse'
  '/gdpr/sar':
    put:
      tags:
        - gdpr
      summary: Subject Access Request
      operationId: getUserData
      description: Retrieve user data for GDPR compliance
      security:
        - apiKeyAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SarRequest'
      responses:
        '200':
          description: User data retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SarResponse'
        default:
          $ref: '#/components/responses/ApiErrorResponse'
  '/gdpr/ddr':
    put:
      tags:
        - gdpr
      summary: Data Deletion Request
      operationId: deleteUserData
      description: Delete user data for GDPR compliance
      security:
        - apiKeyAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DdrRequest'
      responses:
        '200':
          description: User data deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DdrResponse'
        default:
          $ref: '#/components/responses/ApiErrorResponse'
  '/admin/verification-requests':
    get:
      tags:
        - admin
      operationId: getVerificationRequests
      summary: Get verification requests for admin review
      description: Retrieve verification requests for customer service review
      security:
        - csApiKeyAuth: []
      parameters:
        - name: status
          in: query
          description: Filter by verification status
          schema:
            $ref: '#/components/schemas/VerificationStatus'
        - name: user-id
          in: query
          description: Filter by user ID
          schema:
            type: integer
            format: int64
        - name: account-id
          in: query
          description: Filter by account ID
          schema:
            type: integer
            format: int64
        - name: page
          in: query
          description: Page number for pagination
          schema:
            type: integer
            default: 0
        - name: size
          in: query
          description: Page size for pagination
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: Verification requests retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VerificationRequestsPageResponse'
        default:
          $ref: '#/components/responses/ApiErrorResponse'
  '/company-search/search':
    post:
      tags:
        - companySearch
      operationId: searchCompanies
      summary: Search for companies
      description: Search for companies using GBG database
      security:
        - bearerAuth: []
      parameters:
        - name: userId
          in: query
          description: User ID performing the search
          schema:
            type: integer
            format: int64
        - name: accountId
          in: query
          description: Account ID performing the search
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CompanySearchRequest'
      responses:
        '200':
          description: Company search completed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanySearchResponse'
        default:
          $ref: '#/components/responses/ApiErrorResponse'
  '/company-search/history':
    get:
      tags:
        - companySearch
      operationId: getSearchHistory
      summary: Get search history
      description: Retrieve search history for a user or account
      security:
        - bearerAuth: []
      parameters:
        - name: userId
          in: query
          description: User ID
          schema:
            type: integer
            format: int64
        - name: accountId
          in: query
          description: Account ID
          schema:
            type: integer
            format: int64
        - name: limit
          in: query
          description: Maximum number of results
          schema:
            type: integer
            default: 10
            minimum: 1
            maximum: 100
      responses:
        '200':
          description: Search history retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanySearchResponse'
        default:
          $ref: '#/components/responses/ApiErrorResponse'
  '/company-search/results/{lookupId}':
    get:
      tags:
        - companySearch
      operationId: getSearchResults
      summary: Get search results by lookup ID
      description: Retrieve search results using GBG lookup ID
      security:
        - bearerAuth: []
      parameters:
        - name: lookupId
          in: path
          description: GBG lookup ID
          required: true
          schema:
            type: integer
      responses:
        '200':
          description: Search results retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompanySearchResponse'
        '404':
          description: Search results not found
        default:
          $ref: '#/components/responses/ApiErrorResponse'
  '/analysis/profiles/{profileUuid}/analysis':
    get:
      tags:
        - analysis
      operationId: getProfileAnalysis
      summary: Get risk analysis for a profile
      description: Retrieve risk analysis results for a GBG profile
      security:
        - bearerAuth: []
      parameters:
        - name: profileUuid
          in: path
          description: GBG Profile UUID
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Analysis results retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AnalysisResponse'
        default:
          $ref: '#/components/responses/ApiErrorResponse'
  '/analysis/profiles/{profileUuid}/analysis/refresh':
    post:
      tags:
        - analysis
      operationId: refreshProfileAnalysis
      summary: Refresh analysis data from GBG
      description: Refresh risk analysis data from GBG API
      security:
        - bearerAuth: []
      parameters:
        - name: profileUuid
          in: path
          description: GBG Profile UUID
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Analysis data refreshed successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AnalysisResponse'
        default:
          $ref: '#/components/responses/ApiErrorResponse'
  '/analysis/profiles/{profileUuid}/risk-summary':
    get:
      tags:
        - analysis
      operationId: getProfileRiskSummary
      summary: Get risk summary for a profile
      description: Get overall risk assessment summary for a profile
      security:
        - bearerAuth: []
      parameters:
        - name: profileUuid
          in: path
          description: GBG Profile UUID
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Risk summary retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RiskSummary'
        default:
          $ref: '#/components/responses/ApiErrorResponse'
  '/analysis/profiles/{profileUuid}/checklists':
    get:
      tags:
        - analysis
      operationId: getProfileChecklists
      summary: Get compliance checklists for a profile
      description: Retrieve compliance checklists for a GBG profile
      security:
        - bearerAuth: []
      parameters:
        - name: profileUuid
          in: path
          description: GBG Profile UUID
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Checklists retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ChecklistResponse'
        default:
          $ref: '#/components/responses/ApiErrorResponse'
  '/analysis/profiles/{profileUuid}/checklists/{checklistId}':
    get:
      tags:
        - analysis
      operationId: getProfileChecklist
      summary: Get specific checklist by ID
      description: Retrieve a specific compliance checklist by ID
      security:
        - bearerAuth: []
      parameters:
        - name: profileUuid
          in: path
          description: GBG Profile UUID
          required: true
          schema:
            type: string
            format: uuid
        - name: checklistId
          in: path
          description: Checklist ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Checklist retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChecklistResponse'
        '404':
          description: Checklist not found
        default:
          $ref: '#/components/responses/ApiErrorResponse'
  '/analysis/profiles/{profileUuid}/compliance/status':
    get:
      tags:
        - analysis
      operationId: isProfileCompliant
      summary: Check if profile is compliant
      description: Check if a profile meets all compliance requirements
      security:
        - bearerAuth: []
      parameters:
        - name: profileUuid
          in: path
          description: GBG Profile UUID
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Compliance status retrieved successfully
          content:
            application/json:
              schema:
                type: boolean
        default:
          $ref: '#/components/responses/ApiErrorResponse'
  '/analysis/profiles/{profileUuid}/compliance/summary':
    get:
      tags:
        - analysis
      operationId: getComplianceSummary
      summary: Get compliance summary for a profile
      description: Get detailed compliance summary for a profile
      security:
        - bearerAuth: []
      parameters:
        - name: profileUuid
          in: path
          description: GBG Profile UUID
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Compliance summary retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComplianceSummary'
        default:
          $ref: '#/components/responses/ApiErrorResponse'
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    apiKeyAuth:
      type: apiKey
      in: header
      name: X-Service-Key
    csApiKeyAuth:
      type: apiKey
      in: header
      name: X-CS-Api-Key
  responses:
    ApiErrorResponse:
      description: Generic API error response
      content:
        application/problem+json:
          schema:
            $ref: '#/components/schemas/ApiError'
  schemas:
    InitiateVerificationRequest:
      type: object
      description: Request to initiate user identity verification
      required:
        - verificationType
        - personalInfo
      properties:
        verificationType:
          $ref: '#/components/schemas/VerificationType'
        personalInfo:
          $ref: '#/components/schemas/PersonalInfo'
        customerReference:
          type: string
          description: Customer reference for tracking
          example: "user_12345_kyc"
    InitiateBusinessVerificationRequest:
      type: object
      description: Request to initiate business identity verification
      required:
        - verificationType
        - businessInfo
      properties:
        verificationType:
          $ref: '#/components/schemas/VerificationType'
        businessInfo:
          $ref: '#/components/schemas/BusinessInfo'
        customerReference:
          type: string
          description: Customer reference for tracking
          example: "account_67890_kyb"
    VerificationResponse:
      type: object
      description: Response for verification request
      properties:
        verificationId:
          type: string
          format: uuid
          description: Unique verification request ID
          example: "550e8400-e29b-41d4-a716-************"
        status:
          $ref: '#/components/schemas/VerificationStatus'
        gbgProfileId:
          type: string
          description: GBG profile ID
          example: "GBG-12345"
        createdAt:
          type: string
          format: date-time
          description: Creation timestamp
          example: "2023-01-01T10:00:00Z"
        estimatedCompletionTime:
          type: string
          format: date-time
          description: Estimated completion time
          example: "2023-01-01T12:00:00Z"
    VerificationStatusResponse:
      type: object
      description: Verification status response
      properties:
        verificationId:
          type: string
          format: uuid
          description: Verification request ID
          example: "550e8400-e29b-41d4-a716-************"
        userId:
          type: integer
          format: int64
          description: User ID (for KYC)
          example: 12345
        accountId:
          type: integer
          format: int64
          description: Account ID (for KYB)
          example: 67890
        status:
          $ref: '#/components/schemas/VerificationStatus'
        riskScore:
          type: integer
          minimum: 0
          maximum: 100
          description: Risk score (0-100)
          example: 75
        riskLevel:
          $ref: '#/components/schemas/RiskLevel'
        completedSteps:
          type: array
          items:
            $ref: '#/components/schemas/VerificationStep'
        nextSteps:
          type: array
          items:
            $ref: '#/components/schemas/VerificationStep'
        createdAt:
          type: string
          format: date-time
          description: Creation timestamp
          example: "2023-01-01T10:00:00Z"
        updatedAt:
          type: string
          format: date-time
          description: Last update timestamp
          example: "2023-01-01T11:00:00Z"
        completedAt:
          type: string
          format: date-time
          description: Completion timestamp
          example: "2023-01-01T12:00:00Z"
    DocumentUploadRequest:
      type: object
      description: Document upload request
      required:
        - documentType
        - file
      properties:
        documentType:
          $ref: '#/components/schemas/DocumentType'
        file:
          type: string
          format: binary
          description: Document file
          example: !!binary |-
            Y2FjY2E=
        description:
          type: string
          description: Document description
          example: "Passport copy for verification"
    DocumentResponse:
      type: object
      description: Document upload response
      properties:
        documentId:
          type: string
          format: uuid
          description: Document ID
          example: "550e8400-e29b-41d4-a716-************"
        documentType:
          $ref: '#/components/schemas/DocumentType'
        status:
          $ref: '#/components/schemas/DocumentStatus'
        uploadedAt:
          type: string
          format: date-time
          description: Upload timestamp
          example: "2023-01-01T10:00:00Z"
    PersonalInfo:
      type: object
      description: Personal information for KYC
      required:
        - firstName
        - lastName
        - dateOfBirth
      properties:
        firstName:
          type: string
          description: First name
          example: "John"
        lastName:
          type: string
          description: Last name
          example: "Doe"
        middleName:
          type: string
          description: Middle name
          example: "Michael"
        dateOfBirth:
          type: string
          format: date
          description: Date of birth
          example: "1990-01-15"
        nationality:
          type: string
          description: Nationality
          example: "British"
        address:
          $ref: '#/components/schemas/Address'
    BusinessInfo:
      type: object
      description: Business information for KYB
      required:
        - companyName
        - companyNumber
        - address
      properties:
        companyName:
          type: string
          description: Company name
          example: "Acme Corporation Ltd"
        companyNumber:
          type: string
          description: Company registration number
          example: "12345678"
        address:
          $ref: '#/components/schemas/Address'
        domain:
          type: string
          description: Company domain
          example: "acme.com"
        representatives:
          type: array
          items:
            $ref: '#/components/schemas/RepresentativeInfo'
    RepresentativeInfo:
      type: object
      description: Representative information
      required:
        - type
        - personalInfo
      properties:
        type:
          $ref: '#/components/schemas/RepresentativeType'
        personalInfo:
          $ref: '#/components/schemas/PersonalInfo'
    Address:
      type: object
      description: Address information
      required:
        - countryCode
      properties:
        street:
          type: string
          description: Street address
          example: "123 Main Street"
        city:
          type: string
          description: City
          example: "London"
        state:
          type: string
          description: State or region
          example: "England"
        postalCode:
          type: string
          description: Postal code
          example: "SW1A 1AA"
        countryCode:
          type: string
          description: ISO country code
          example: "GB"
    # Enum schemas
    VerificationType:
      type: string
      enum:
        - KYC
        - KYB
      description: Type of verification
      example: KYC
    VerificationStatus:
      type: string
      enum:
        - INITIATED
        - DOCUMENTS_REQUIRED
        - DOCUMENTS_UPLOADED
        - IN_PROGRESS
        - UNDER_REVIEW
        - COMPLETED
        - APPROVED
        - REJECTED
        - EXPIRED
      description: Verification status
      example: INITIATED
    RiskLevel:
      type: string
      enum:
        - LOW
        - MEDIUM
        - HIGH
        - CRITICAL
      description: Risk level assessment
      example: LOW
    DocumentType:
      type: string
      enum:
        - PASSPORT
        - DRIVING_LICENSE
        - NATIONAL_ID
        - UTILITY_BILL
        - BANK_STATEMENT
        - COMPANY_REGISTRATION
        - MEMORANDUM_OF_ASSOCIATION
        - ARTICLES_OF_ASSOCIATION
      description: Document type for verification
      example: PASSPORT
    DocumentStatus:
      type: string
      enum:
        - UPLOADED
        - PROCESSING
        - VERIFIED
        - REJECTED
      description: Document verification status
      example: UPLOADED
    RepresentativeType:
      type: string
      enum:
        - DIRECTOR
        - IDENTITY
        - OWNERSHIP
        - DECLARATORY
        - PRINCIPAL
        - DIRECTOR_COMPANY
      description: Representative type as per GBG classification
      example: DIRECTOR
    VerificationStep:
      type: object
      description: Verification step information
      properties:
        stepType:
          type: string
          enum:
            - DOCUMENT_UPLOAD
            - IDENTITY_VERIFICATION
            - LIVENESS_CHECK
            - DIGITAL_FOOTPRINT
            - RISK_ASSESSMENT
          description: Type of verification step
          example: "DOCUMENT_UPLOAD"
        status:
          type: string
          enum:
            - PENDING
            - IN_PROGRESS
            - COMPLETED
            - FAILED
          description: Step status
          example: "COMPLETED"
        completedAt:
          type: string
          format: date-time
          description: Step completion timestamp
          example: "2023-01-01T11:00:00Z"
    # GDPR Schemas
    SarRequest:
      type: object
      description: Subject Access Request
      required:
        - requestId
        - subtaskId
        - email
      properties:
        requestId:
          type: string
          description: Request ID
          example: "REQ-1234"
        subtaskId:
          type: string
          description: Sub Task ID
          example: "e58ed763-928c-4155-bee9-fdbaaadc15a5"
        email:
          type: string
          description: User email address
          example: "<EMAIL>"
    SarResponse:
      type: object
      description: Subject Access Response
      required:
        - requestId
        - subtaskId
      properties:
        requestId:
          type: string
          description: Request ID
          example: "REQ-1234"
        subtaskId:
          type: string
          description: Sub Task ID
          example: "e58ed763-928c-4155-bee9-fdbaaadc15a5"
        error:
          type: boolean
          description: Error flag
          default: false
          example: false
        reason:
          type: string
          description: Error reason if any
          example: "Request processed successfully"
        salutation:
          type: string
          description: User salutation
          example: "Mr."
        personalDataSegments:
          type: array
          items:
            $ref: '#/components/schemas/PersonalDataSegment'
    DdrRequest:
      type: object
      description: Data Deletion Request
      required:
        - requestId
        - subtaskId
        - email
      properties:
        requestId:
          type: string
          description: Request ID
          example: "REQ-1234"
        subtaskId:
          type: string
          description: Sub Task ID
          example: "e58ed763-928c-4155-bee9-fdbaaadc15a5"
        email:
          type: string
          description: User email address
          example: "<EMAIL>"
    DdrResponse:
      type: object
      description: Data Deletion Response
      required:
        - requestId
        - subtaskId
      properties:
        requestId:
          type: string
          description: Request ID
          example: "REQ-1234"
        subtaskId:
          type: string
          description: Sub Task ID
          example: "e58ed763-928c-4155-bee9-fdbaaadc15a5"
        error:
          type: boolean
          description: Error flag
          default: false
          example: false
        reason:
          type: string
          description: Error reason if any
          example: "Data deleted successfully"
    PersonalDataSegment:
      type: object
      description: Personal data segment for GDPR response
      required:
        - title
        - description
        - values
      properties:
        title:
          type: string
          description: Data segment title
          example: "Identity Verification Records"
        description:
          type: string
          description: Data segment description
          example: "Records of identity verification attempts and results"
        values:
          type: array
          items:
            type: object
            additionalProperties:
              type: string
          description: Data values
          example: [{"name": "John Doe", "email": "<EMAIL>"}]
    VerificationRequestsPageResponse:
      type: object
      description: Paginated verification requests response
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/VerificationStatusResponse'
        totalElements:
          type: integer
          format: int64
          description: Total number of elements
          example: 100
        totalPages:
          type: integer
          description: Total number of pages
          example: 10
        size:
          type: integer
          description: Page size
          example: 10
        number:
          type: integer
          description: Current page number
          example: 0
    # Common Error Schema
    ApiError:
      type: object
      description: Standard API error response
      required:
        - timestamp
        - status
        - error
        - message
        - path
      properties:
        timestamp:
          type: string
          format: date-time
          description: Timestamp when the error occurred
          example: "2023-12-01T10:30:00Z"
        status:
          type: integer
          description: HTTP status code
          example: 400
        error:
          type: string
          description: Error type/category
          example: "Bad Request"
        message:
          type: string
          description: Error message
          example: "Validation failed for request"
        type:
          type: string
          description: Problem type URI (RFC 7807)
          example: "https://api.gumtree.com/problems/validation-error"
        title:
          type: string
          description: Short, human-readable summary of the problem
          example: "Validation Error"
        detail:
          type: string
          description: Human-readable explanation specific to this occurrence
          example: "The firstName field is required but was not provided"
        instance:
          type: string
          description: URI reference that identifies the specific occurrence
          example: "/users/12345/identity-verification"
        code:
          type: string
          description: Application-specific error code
          example: "VALIDATION_FAILED"
        path:
          type: string
          description: Request path where error occurred
          example: "/users/12345/identity-verification"
        validationErrors:
          type: object
          additionalProperties:
            type: string
          description: Field-specific validation errors
          example:
            firstName: "First name is required"
            email: "Invalid email format"
        details:
          type: array
          items:
            $ref: '#/components/schemas/ApiErrorDetail'
          description: Detailed error information for specific fields
          example:
            - field: "firstName"
              message: "First name is required"
              location: "BODY"
        traceId:
          type: string
          description: Trace ID for debugging
          example: "abc123-def456-ghi789"

    ApiErrorDetail:
      type: object
      description: Detailed error information for specific fields or locations
      required:
        - field
        - message
        - location
      properties:
        field:
          type: string
          description: Field name that caused the error
          example: "firstName"
        message:
          type: string
          description: Error message for this field
          example: "First name is required"
        location:
          $ref: '#/components/schemas/LocationEnum'
        value:
          type: string
          description: The rejected value
          example: ""

    LocationEnum:
      type: string
      enum:
        - BODY
        - HEADER
        - PATH
        - QUERY
      description: Location where the error occurred

    # Company Search Schemas
    CompanySearchRequest:
      type: object
      description: Request to search for companies
      required:
        - type
        - name
        - address
      properties:
        type:
          type: string
          enum: [company]
          description: Search type
          example: "company"
        name:
          type: string
          description: Company name to search for
          example: "Acme Corporation Ltd"
        vatNumber:
          type: string
          description: VAT number
          example: "GB123456789"
        companyReference:
          type: string
          description: Company reference number
          example: "12345678"
        address:
          $ref: '#/components/schemas/SearchAddress'

    SearchAddress:
      type: object
      description: Address information for search
      required:
        - countryCode
        - city
      properties:
        line1:
          type: string
          description: Address line 1
          example: "123 Main Street"
        line2:
          type: string
          description: Address line 2
          example: "Suite 100"
        countryCode:
          type: string
          pattern: '^[A-Z]{2}$'
          description: ISO 3166-1 alpha-2 country code
          example: "GB"
        town:
          type: string
          description: Town
          example: "Westminster"
        city:
          type: string
          description: City
          example: "London"
        postCode:
          type: string
          description: Postal code
          example: "SW1A 1AA"
        province:
          type: string
          description: Province
          example: "Greater London"
        state:
          type: string
          description: State
          example: "England"

    CompanySearchResponse:
      type: object
      description: Response from company search
      properties:
        lookupId:
          type: integer
          description: GBG lookup ID for this search
          example: 12345
        results:
          type: array
          items:
            $ref: '#/components/schemas/CompanySearchResult'
        totalResults:
          type: integer
          description: Total number of results
          example: 5
        searchedAt:
          type: string
          format: date-time
          description: When the search was performed
          example: "2023-01-01T10:00:00Z"

    CompanySearchResult:
      type: object
      description: Individual company search result
      properties:
        responseId:
          type: integer
          description: Response ID from GBG
          example: 1
        type:
          type: string
          description: Result type
          example: "company"
        name:
          type: string
          description: Company name
          example: "Acme Corporation Ltd"
        companyReference:
          type: string
          description: Company reference number
          example: "12345678"
        provider:
          type: string
          description: Data provider
          example: "Companies House"
        providerReference:
          type: string
          description: Provider reference
          example: "CH-12345678"
        jurisdictionCode:
          type: string
          description: Jurisdiction code
          example: "GB"
        companyType:
          type: string
          description: Company type
          example: "Private Limited Company"
        currentStatus:
          type: string
          description: Current company status
          example: "Active"
        address:
          $ref: '#/components/schemas/SearchAddressResult'
        matchScore:
          type: number
          format: double
          minimum: 0
          maximum: 100
          description: Match score (0-100)
          example: 95.5

    SearchAddressResult:
      type: object
      description: Address result from search
      properties:
        line1:
          type: string
          example: "123 Main Street"
        line2:
          type: string
          example: "Suite 100"
        countryCode:
          type: string
          example: "GB"
        town:
          type: string
          example: "Westminster"
        city:
          type: string
          example: "London"
        postCode:
          type: string
          example: "SW1A 1AA"
        province:
          type: string
          example: "Greater London"
        state:
          type: string
          example: "England"
        fullAddress:
          type: string
          description: Complete formatted address
          example: "123 Main Street, Suite 100, London, Greater London SW1A 1AA, GB"

    # Analysis Schemas
    AnalysisResponse:
      type: object
      description: Risk analysis response
      properties:
        profileId:
          type: string
          format: uuid
          description: GBG Profile UUID
          example: "550e8400-e29b-41d4-a716-************"
        analysisId:
          type: string
          description: Analysis ID
          example: "analysis_1234567890_75"
        score:
          type: integer
          minimum: 0
          maximum: 100
          description: Risk score (0-100)
          example: 75
        status:
          type: string
          enum: [failed, completed, pending, missing_data]
          description: Analysis status
          example: "completed"
        riskLevel:
          type: string
          enum: [Low, Medium, High]
          description: Risk level
          example: "Medium"
        createdAt:
          type: string
          format: date-time
          description: Analysis creation time
          example: "2023-01-01T10:00:00Z"
        updatedAt:
          type: string
          format: date-time
          description: Analysis update time
          example: "2023-01-01T10:30:00Z"
        reports:
          type: array
          items:
            $ref: '#/components/schemas/AnalysisReport'
        riskSummary:
          $ref: '#/components/schemas/RiskSummary'

    AnalysisReport:
      type: object
      description: Analysis report section
      properties:
        name:
          type: string
          description: Report name
          example: "Company Verification"
        description:
          type: string
          description: Report description
          example: "Analysis report for Company Verification"
        results:
          type: array
          items:
            $ref: '#/components/schemas/AnalysisResult'
        overallResult:
          type: string
          enum: [pass, fail, warning, info]
          description: Overall result
          example: "pass"
        overallScore:
          type: integer
          minimum: 0
          maximum: 100
          description: Overall score
          example: 85

    AnalysisResult:
      type: object
      description: Individual analysis result
      properties:
        id:
          type: string
          description: Result ID
          example: "check_001"
        label:
          type: string
          description: Result label
          example: "Company Status Check"
        description:
          type: string
          description: Result description
          example: "Verify company is active and in good standing"
        score:
          type: integer
          minimum: 0
          maximum: 100
          description: Score for this check
          example: 90
        state:
          type: string
          enum: [skipped, analysed, failed]
          description: Check state
          example: "analysed"
        result:
          type: string
          enum: [pass, fail, warning, info]
          description: Check result
          example: "pass"
        details:
          type: string
          description: Detailed information
          example: "Company is active and registered"
        recommendations:
          type: array
          items:
            type: string
          description: Recommendations based on result
          example: ["Continue with verification process"]

    RiskSummary:
      type: object
      description: Risk assessment summary
      properties:
        totalScore:
          type: integer
          minimum: 0
          maximum: 100
          description: Total risk score
          example: 75
        riskCategory:
          type: string
          enum: [Low, Medium, High, Unknown]
          description: Risk category
          example: "Medium"
        riskLevel:
          type: string
          description: Detailed risk level
          example: "Medium"
        riskFactors:
          type: array
          items:
            $ref: '#/components/schemas/RiskFactor'
        recommendations:
          type: array
          items:
            type: string
          description: Risk mitigation recommendations
          example: ["Enhanced due diligence recommended", "Monitor for changes"]
        requiresManualReview:
          type: boolean
          description: Whether manual review is required
          example: false
        reviewReason:
          type: string
          description: Reason for manual review requirement
          example: "High risk score requires manual review"

    RiskFactor:
      type: object
      description: Individual risk factor
      properties:
        category:
          type: string
          description: Risk factor category
          example: "Financial"
        factor:
          type: string
          description: Specific risk factor
          example: "High debt ratio"
        impact:
          type: integer
          minimum: 1
          maximum: 10
          description: Impact level (1-10)
          example: 7
        description:
          type: string
          description: Factor description
          example: "Company has high debt-to-equity ratio"
        mitigation:
          type: string
          description: Suggested mitigation
          example: "Request additional financial documentation"

    # Compliance Schemas
    ChecklistResponse:
      type: object
      description: Compliance checklist response
      properties:
        profileId:
          type: string
          format: uuid
          description: GBG Profile UUID
          example: "550e8400-e29b-41d4-a716-************"
        checklistId:
          type: integer
          description: Checklist ID
          example: 1
        name:
          type: string
          description: Checklist name
          example: "KYB Compliance Checklist"
        description:
          type: string
          description: Checklist description
          example: "Compliance checklist for KYB verification"
        uncompletedTasks:
          type: integer
          minimum: 0
          description: Number of uncompleted tasks
          example: 2
        completedTasks:
          type: integer
          minimum: 0
          description: Number of completed tasks
          example: 8
        totalTasks:
          type: integer
          minimum: 0
          description: Total number of tasks
          example: 10
        completionPercentage:
          type: number
          format: double
          minimum: 0
          maximum: 100
          description: Completion percentage
          example: 80.0
        overallStatus:
          type: string
          enum: [exempt, failed, passed, needs_review]
          description: Overall checklist status
          example: "needs_review"
        createdAt:
          type: string
          format: date-time
          description: Creation timestamp
          example: "2023-01-01T10:00:00Z"
        updatedAt:
          type: string
          format: date-time
          description: Last update timestamp
          example: "2023-01-01T15:30:00Z"
        sections:
          type: array
          items:
            $ref: '#/components/schemas/ChecklistSection'
        complianceSummary:
          $ref: '#/components/schemas/ComplianceSummary'

    ChecklistSection:
      type: object
      description: Checklist section
      properties:
        sectionId:
          type: integer
          description: Section ID
          example: 1
        name:
          type: string
          description: Section name
          example: "Company Documentation"
        description:
          type: string
          description: Section description
          example: "Verify company registration and legal documents"
        status:
          type: string
          enum: [exempt, failed, passed, needs_review]
          description: Section status
          example: "passed"
        totalComments:
          type: integer
          minimum: 0
          description: Number of comments
          example: 3
        memberBelongsToTeam:
          type: boolean
          description: Whether member belongs to assigned team
          example: true
        tasks:
          type: array
          items:
            $ref: '#/components/schemas/ChecklistTask'
        comments:
          type: array
          items:
            $ref: '#/components/schemas/ChecklistComment'

    ChecklistTask:
      type: object
      description: Individual checklist task
      properties:
        taskId:
          type: integer
          description: Task ID
          example: 1
        name:
          type: string
          description: Task name
          example: "Verify Company Registration"
        description:
          type: string
          description: Task description
          example: "Verify company is properly registered with authorities"
        status:
          type: string
          enum: [pending, completed, failed, skipped]
          description: Task status
          example: "completed"
        assignedTeam:
          type: string
          description: Assigned team name
          example: "Compliance Team"
        teamId:
          type: integer
          description: Team ID
          example: 1
        assignedUser:
          type: string
          description: Assigned user
          example: "John Smith"
        dueDate:
          type: string
          format: date-time
          description: Task due date
          example: "2023-01-02T17:00:00Z"
        completedAt:
          type: string
          format: date-time
          description: Task completion time
          example: "2023-01-01T16:30:00Z"
        completionNotes:
          type: string
          description: Notes from task completion
          example: "Company registration verified successfully"
        requiredDocuments:
          type: array
          items:
            type: string
          description: Required documents for this task
          example: ["Certificate of Incorporation", "Memorandum of Association"]
        attachedDocuments:
          type: array
          items:
            type: string
          description: Documents attached to this task
          example: ["cert_incorporation.pdf", "memorandum.pdf"]

    ChecklistComment:
      type: object
      description: Checklist comment
      properties:
        commentId:
          type: integer
          description: Comment ID
          example: 1
        content:
          type: string
          description: Comment content
          example: "Additional documentation required for verification"
        authorName:
          type: string
          description: Comment author name
          example: "Jane Doe"
        authorRole:
          type: string
          description: Author role
          example: "Compliance Officer"
        createdAt:
          type: string
          format: date-time
          description: Comment creation time
          example: "2023-01-01T14:30:00Z"
        commentType:
          type: string
          enum: [note, question, approval, rejection]
          description: Type of comment
          example: "note"
        isInternal:
          type: boolean
          description: Whether comment is internal only
          example: true

    ComplianceSummary:
      type: object
      description: Compliance summary
      properties:
        isCompliant:
          type: boolean
          description: Whether profile is compliant
          example: false
        complianceStatus:
          type: string
          enum: [compliant, non_compliant, pending_review, incomplete]
          description: Compliance status
          example: "pending_review"
        missingRequirements:
          type: array
          items:
            type: string
          description: Missing compliance requirements
          example: ["Director verification", "Financial statements"]
        failedChecks:
          type: array
          items:
            type: string
          description: Failed compliance checks
          example: ["Address verification"]
        pendingActions:
          type: array
          items:
            type: string
          description: Pending actions required
          example: ["Review Director verification", "Upload financial statements"]
        complianceScore:
          type: integer
          minimum: 0
          maximum: 100
          description: Compliance score (0-100)
          example: 75
        lastReviewDate:
          type: string
          format: date-time
          description: Last review date
          example: "2023-01-01T15:30:00Z"
        reviewerName:
          type: string
          description: Name of reviewer
          example: "Jane Smith"
        nextReviewDate:
          type: string
          format: date-time
          description: Next scheduled review date
          example: "2023-01-08T09:00:00Z"
        issues:
          type: array
          items:
            $ref: '#/components/schemas/ComplianceIssue'

    ComplianceIssue:
      type: object
      description: Compliance issue
      properties:
        issueType:
          type: string
          enum: [critical, major, minor, warning]
          description: Issue severity type
          example: "major"
        category:
          type: string
          description: Issue category
          example: "documentation"
        description:
          type: string
          description: Issue description
          example: "Missing required director identification documents"
        resolution:
          type: string
          description: Suggested resolution
          example: "Upload valid passport or driver's license for all directors"
        status:
          type: string
          enum: [open, in_progress, resolved, deferred]
          description: Issue status
          example: "open"
        identifiedAt:
          type: string
          format: date-time
          description: When issue was identified
          example: "2023-01-01T14:00:00Z"
        dueDate:
          type: string
          format: date-time
          description: Issue resolution due date
          example: "2023-01-03T17:00:00Z"
        assignedTo:
          type: string
          description: Person assigned to resolve issue
          example: "Compliance Team"
